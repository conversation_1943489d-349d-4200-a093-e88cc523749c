# 1 "CMakeCUDACompilerId.cu"
# 64 "CMakeCUDACompilerId.cu"
extern const char *info_compiler;

extern const char *info_simulate;
# 329 "CMakeCUDACompilerId.cu"
static const char info_version[50];
# 356 "CMakeCUDACompilerId.cu"
static const char info_simulate_version[41];
# 376 "CMakeCUDACompilerId.cu"
extern const char *info_platform;
extern const char *info_arch;



extern const char *info_language_dialect_default;
# 64 "CMakeCUDACompilerId.cu"
const char *info_compiler = ((const char *)"INFO:compiler[NVIDIA]");

const char *info_simulate = ((const char *)"INFO:simulate[GNU]");
# 329 "CMakeCUDACompilerId.cu"
static const char info_version[50] = {((char)73),((char)78),((char)70),((char)79),((char)58),((char)99),((char)111),((char)109),((char)112),((char)105),((char)108),((char)101),((char)114),((char)95),((char)118),((char)101),((char)114),((char)115),((char)105),((char)111),((char)110),((char)91),((char)48),((char)48),((char)48),((char)48),((char)48),((char)48),((char)49),((char)50),((char)46),((char)48),((char)48),((char)48),((char)48),((char)48),((char)48),((char)48),((char)52),((char)46),((char)48),((char)48),((char)48),((char)48),((char)48),((char)49),((char)51),((char)49),((char)93),((char)0)};
# 356 "CMakeCUDACompilerId.cu"
static const char info_simulate_version[41] = {((char)73),((char)78),((char)70),((char)79),((char)58),((char)115),((char)105),((char)109),((char)117),((char)108),((char)97),((char)116),((char)101),((char)95),((char)118),((char)101),((char)114),((char)115),((char)105),((char)111),((char)110),((char)91),((char)48),((char)48),((char)48),((char)48),((char)48),((char)48),((char)49),((char)51),((char)46),((char)48),((char)48),((char)48),((char)48),((char)48),((char)48),((char)48),((char)50),((char)93),((char)0)};
# 376 "CMakeCUDACompilerId.cu"
const char *info_platform = ((const char *)"INFO:platform[Linux]");
const char *info_arch = ((const char *)"INFO:arch[]");



const char *info_language_dialect_default = ((const char *)"INFO:dialect_default[17]");
