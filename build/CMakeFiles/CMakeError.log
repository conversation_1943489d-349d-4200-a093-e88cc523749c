Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_d9ee6/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_d9ee6.dir/build.make CMakeFiles/cmTC_d9ee6.dir/build
gmake[1]: Entering directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_d9ee6.dir/src.c.o
/usr/bin/cc -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_d9ee6.dir/src.c.o -c /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_d9ee6
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_d9ee6.dir/link.txt --verbose=1
/usr/bin/cc CMakeFiles/cmTC_d9ee6.dir/src.c.o -o cmTC_d9ee6 
/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/bin/ld: CMakeFiles/cmTC_d9ee6.dir/src.c.o: in function `main':
src.c:(.text+0x2d): undefined reference to `pthread_create'
/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/bin/ld: src.c:(.text+0x39): undefined reference to `pthread_detach'
/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/bin/ld: src.c:(.text+0x45): undefined reference to `pthread_cancel'
/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/bin/ld: src.c:(.text+0x56): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
gmake[1]: *** [CMakeFiles/cmTC_d9ee6.dir/build.make:99: cmTC_d9ee6] Error 1
gmake[1]: Leaving directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'
gmake: *** [Makefile:127: cmTC_d9ee6/fast] Error 2


Source file was:
#include <pthread.h>

static void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_cancel(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_d0b45/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_d0b45.dir/build.make CMakeFiles/cmTC_d0b45.dir/build
gmake[1]: Entering directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_d0b45.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create -o CMakeFiles/cmTC_d0b45.dir/CheckFunctionExists.c.o -c /usr/share/cmake/Modules/CheckFunctionExists.c
Linking C executable cmTC_d0b45
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_d0b45.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create CMakeFiles/cmTC_d0b45.dir/CheckFunctionExists.c.o -o cmTC_d0b45  -lpthreads 
/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/bin/ld: cannot find -lpthreads: No such file or directory
collect2: error: ld returned 1 exit status
gmake[1]: *** [CMakeFiles/cmTC_d0b45.dir/build.make:99: cmTC_d0b45] Error 1
gmake[1]: Leaving directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'
gmake: *** [Makefile:127: cmTC_d0b45/fast] Error 2



Performing C SOURCE FILE Test HAVE_IBV_ACCESS_RELAXED_ORDERING failed with the following output:
Change Dir: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_7229d/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_7229d.dir/build.make CMakeFiles/cmTC_7229d.dir/build
gmake[1]: Entering directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_7229d.dir/src.c.o
/usr/bin/cc -DHAVE_IBV_ACCESS_RELAXED_ORDERING   -o CMakeFiles/cmTC_7229d.dir/src.c.o -c /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp/src.c
/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp/src.c:1:10: fatal error: infiniband/verbs.h: No such file or directory
    1 | #include <infiniband/verbs.h>
      |          ^~~~~~~~~~~~~~~~~~~~
compilation terminated.
gmake[1]: *** [CMakeFiles/cmTC_7229d.dir/build.make:78: CMakeFiles/cmTC_7229d.dir/src.c.o] Error 1
gmake[1]: Leaving directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'
gmake: *** [Makefile:127: cmTC_7229d/fast] Error 2


Source file was:
#include <infiniband/verbs.h>
  int main(void) { int x = IBV_ACCESS_RELAXED_ORDERING; return 1; }
