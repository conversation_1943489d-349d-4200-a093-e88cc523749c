The system is: Linux - 5.14.21-150500.55.97_13.0.78-cray_shasta_c - x86_64
Checking whether the CUDA compiler is NVIDIA using "" matched "nvcc: NVIDIA \(R\) Cuda compiler driver":
nvcc: NVIDIA (R) Cuda compiler driver
Copyright (c) 2005-2024 NVIDIA Corporation
Built on Thu_Mar_28_02:18:24_PDT_2024
Cuda compilation tools, release 12.4, V12.4.131
Build cuda_12.4.r12.4/compiler.34097967_0
Compiling the CUDA compiler identification source file "CMakeCUDACompilerId.cu" succeeded.
Compiler: /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/nvcc 
Build flags: 
Id flags: --keep;--keep-dir;tmp;-gencode=arch=compute_80,code=sm_80 -v

The output was:
0
#$ _NVVM_BRANCH_=nvvm
#$ _NVVM_BRANCH_SUFFIX_=
#$ _SPACE_= 
#$ _CUDART_=cudart
#$ _HERE_=/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin
#$ _THERE_=/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin
#$ _TARGET_SIZE_=
#$ _TARGET_DIR_=
#$ _TARGET_DIR_=targets/x86_64-linux
#$ TOP=/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/..
#$ NVVMIR_LIBRARY_DIR=/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../nvvm/libdevice
#$ LD_LIBRARY_PATH=/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../lib:/global/common/software/nersc9/darshan/default/lib:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/lib64:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/extras/CUPTI/lib64:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/extras/Debugger/lib64:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/nvvm/lib64:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/lib64:/opt/cray/pe/papi/7.1.0.2/lib64:/opt/cray/libfabric/1.20.1/lib64
#$ PATH=/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../nvvm/bin:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin:/global/homes/j/jackyan/workspace/spack/bin:/opt/nersc/pe/bin:/global/common/software/nersc/bin:/global/common/software/nersc9/darshan/default/bin:/global/common/software/nersc9/sqs/2.0/bin:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/compute-sanitizer:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/libnvvp:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/profilers/Nsight_Compute:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/profilers/Nsight_Systems/bin:/opt/cray/pe/perftools/24.07.0/bin:/opt/cray/pe/papi/7.1.0.2/bin:/opt/cray/pe/gcc-native/13/bin:/opt/cray/pe/craype/2.7.32/bin:/opt/cray/pe/mpich/8.1.30/ofi/gnu/12.3/bin:/opt/cray/pe/mpich/8.1.30/bin:/opt/cray/libfabric/1.20.1/bin:/usr/local/bin:/usr/bin:/bin:/usr/lib/mit/bin:/opt/cray/pe/bin
#$ INCLUDES="-I/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include"  
#$ LIBRARIES=  "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs" "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib"
#$ CUDAFE_FLAGS=
#$ PTXAS_FLAGS=
#$ rm tmp/a_dlink.reg.c
#$ gcc -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -E -x c++ -D__CUDACC__ -D__NVCC__  "-I/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include"    -D__CUDACC_VER_MAJOR__=12 -D__CUDACC_VER_MINOR__=4 -D__CUDACC_VER_BUILD__=131 -D__CUDA_API_VER_MAJOR__=12 -D__CUDA_API_VER_MINOR__=4 -D__NVCC_DIAG_PRAGMA_SUPPORT__=1 -include "cuda_runtime.h" -m64 "CMakeCUDACompilerId.cu" -o "tmp/CMakeCUDACompilerId.cpp4.ii" 
#$ cudafe++ --c++17 --gnu_version=130201 --display_error_number --orig_src_file_name "CMakeCUDACompilerId.cu" --orig_src_path_name "/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/3.20.4/CompilerIdCUDA/CMakeCUDACompilerId.cu" --allow_managed  --m64 --parse_templates --gen_c_file_name "tmp/CMakeCUDACompilerId.cudafe1.cpp" --stub_file_name "CMakeCUDACompilerId.cudafe1.stub.c" --gen_module_id_file --module_id_file_name "tmp/CMakeCUDACompilerId.module_id" "tmp/CMakeCUDACompilerId.cpp4.ii" 
#$ gcc -D__CUDA_ARCH__=800 -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -E -x c++  -DCUDA_DOUBLE_MATH_FUNCTIONS -D__CUDACC__ -D__NVCC__  "-I/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include"    -D__CUDACC_VER_MAJOR__=12 -D__CUDACC_VER_MINOR__=4 -D__CUDACC_VER_BUILD__=131 -D__CUDA_API_VER_MAJOR__=12 -D__CUDA_API_VER_MINOR__=4 -D__NVCC_DIAG_PRAGMA_SUPPORT__=1 -include "cuda_runtime.h" -m64 "CMakeCUDACompilerId.cu" -o "tmp/CMakeCUDACompilerId.cpp1.ii" 
#$ cicc --c++17 --gnu_version=130201 --display_error_number --orig_src_file_name "CMakeCUDACompilerId.cu" --orig_src_path_name "/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/3.20.4/CompilerIdCUDA/CMakeCUDACompilerId.cu" --allow_managed   -arch compute_80 -m64 --no-version-ident -ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 --include_file_name "CMakeCUDACompilerId.fatbin.c" -tused --module_id_file_name "tmp/CMakeCUDACompilerId.module_id" --gen_c_file_name "tmp/CMakeCUDACompilerId.cudafe1.c" --stub_file_name "tmp/CMakeCUDACompilerId.cudafe1.stub.c" --gen_device_file_name "tmp/CMakeCUDACompilerId.cudafe1.gpu"  "tmp/CMakeCUDACompilerId.cpp1.ii" -o "tmp/CMakeCUDACompilerId.ptx"
#$ ptxas -arch=sm_80 -m64  "tmp/CMakeCUDACompilerId.ptx"  -o "tmp/CMakeCUDACompilerId.cubin" 
#$ fatbinary --create="tmp/CMakeCUDACompilerId.fatbin" -64 --cicc-cmdline="-ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 " "--image3=kind=elf,sm=80,file=tmp/CMakeCUDACompilerId.cubin" --embedded-fatbin="tmp/CMakeCUDACompilerId.fatbin.c" 
#$ gcc -D__CUDA_ARCH__=800 -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -c -x c++  -DCUDA_DOUBLE_MATH_FUNCTIONS -Wno-psabi "-I/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include"   -m64 "tmp/CMakeCUDACompilerId.cudafe1.cpp" -o "tmp/CMakeCUDACompilerId.o" 
#$ nvlink -m64 --arch=sm_80 --register-link-binaries="tmp/a_dlink.reg.c"    "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs" "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib" -cpu-arch=X86_64 "tmp/CMakeCUDACompilerId.o"  -lcudadevrt  -o "tmp/a_dlink.cubin" --host-ccbin "gcc"
#$ fatbinary --create="tmp/a_dlink.fatbin" -64 --cicc-cmdline="-ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 " -link "--image3=kind=elf,sm=80,file=tmp/a_dlink.cubin" --embedded-fatbin="tmp/a_dlink.fatbin.c" 
#$ gcc -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -c -x c++ -DFATBINFILE="\"tmp/a_dlink.fatbin.c\"" -DREGISTERLINKBINARYFILE="\"tmp/a_dlink.reg.c\"" -I. -D__NV_EXTRA_INITIALIZATION= -D__NV_EXTRA_FINALIZATION= -D__CUDA_INCLUDE_COMPILER_INTERNAL_HEADERS__  -Wno-psabi "-I/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include"    -D__CUDACC_VER_MAJOR__=12 -D__CUDACC_VER_MINOR__=4 -D__CUDACC_VER_BUILD__=131 -D__CUDA_API_VER_MAJOR__=12 -D__CUDA_API_VER_MINOR__=4 -D__NVCC_DIAG_PRAGMA_SUPPORT__=1 -m64 "/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/crt/link.stub" -o "tmp/a_dlink.o" 
#$ g++ -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -m64 -Wl,--start-group "tmp/a_dlink.o" "tmp/CMakeCUDACompilerId.o"   "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs" "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib"  -lcudadevrt  -lcudart_static  -lrt -lpthread  -ldl  -Wl,--end-group -o "a.out" 


Compilation of the CUDA compiler identification source "CMakeCUDACompilerId.cu" produced "a.out"

The CUDA compiler identification is NVIDIA, found in "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/3.20.4/CompilerIdCUDA/a.out"

Parsed CUDA nvcc implicit link information from above output:
  found 'PATH=' string: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../nvvm/bin:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin:/global/homes/j/jackyan/workspace/spack/bin:/opt/nersc/pe/bin:/global/common/software/nersc/bin:/global/common/software/nersc9/darshan/default/bin:/global/common/software/nersc9/sqs/2.0/bin:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/compute-sanitizer:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/libnvvp:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/profilers/Nsight_Compute:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/profilers/Nsight_Systems/bin:/opt/cray/pe/perftools/24.07.0/bin:/opt/cray/pe/papi/7.1.0.2/bin:/opt/cray/pe/gcc-native/13/bin:/opt/cray/pe/craype/2.7.32/bin:/opt/cray/pe/mpich/8.1.30/ofi/gnu/12.3/bin:/opt/cray/pe/mpich/8.1.30/bin:/opt/cray/libfabric/1.20.1/bin:/usr/local/bin:/usr/bin:/bin:/usr/lib/mit/bin:/opt/cray/pe/bin]
  found 'LIBRARIES=' string: ["-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs" "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib"]
  considering line: [#$ rm tmp/a_dlink.reg.c]
  considering line: [gcc -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -E -x c++ -D__CUDACC__ -D__NVCC__  "-I/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include"    -D__CUDACC_VER_MAJOR__=12 -D__CUDACC_VER_MINOR__=4 -D__CUDACC_VER_BUILD__=131 -D__CUDA_API_VER_MAJOR__=12 -D__CUDA_API_VER_MINOR__=4 -D__NVCC_DIAG_PRAGMA_SUPPORT__=1 -include "cuda_runtime.h" -m64 "CMakeCUDACompilerId.cu" -o "tmp/CMakeCUDACompilerId.cpp4.ii" ]
  considering line: [cudafe++ --c++17 --gnu_version=130201 --display_error_number --orig_src_file_name "CMakeCUDACompilerId.cu" --orig_src_path_name "/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/3.20.4/CompilerIdCUDA/CMakeCUDACompilerId.cu" --allow_managed  --m64 --parse_templates --gen_c_file_name "tmp/CMakeCUDACompilerId.cudafe1.cpp" --stub_file_name "CMakeCUDACompilerId.cudafe1.stub.c" --gen_module_id_file --module_id_file_name "tmp/CMakeCUDACompilerId.module_id" "tmp/CMakeCUDACompilerId.cpp4.ii" ]
  considering line: [gcc -D__CUDA_ARCH__=800 -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -E -x c++  -DCUDA_DOUBLE_MATH_FUNCTIONS -D__CUDACC__ -D__NVCC__  "-I/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include"    -D__CUDACC_VER_MAJOR__=12 -D__CUDACC_VER_MINOR__=4 -D__CUDACC_VER_BUILD__=131 -D__CUDA_API_VER_MAJOR__=12 -D__CUDA_API_VER_MINOR__=4 -D__NVCC_DIAG_PRAGMA_SUPPORT__=1 -include "cuda_runtime.h" -m64 "CMakeCUDACompilerId.cu" -o "tmp/CMakeCUDACompilerId.cpp1.ii" ]
  considering line: [cicc --c++17 --gnu_version=130201 --display_error_number --orig_src_file_name "CMakeCUDACompilerId.cu" --orig_src_path_name "/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/3.20.4/CompilerIdCUDA/CMakeCUDACompilerId.cu" --allow_managed   -arch compute_80 -m64 --no-version-ident -ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 --include_file_name "CMakeCUDACompilerId.fatbin.c" -tused --module_id_file_name "tmp/CMakeCUDACompilerId.module_id" --gen_c_file_name "tmp/CMakeCUDACompilerId.cudafe1.c" --stub_file_name "tmp/CMakeCUDACompilerId.cudafe1.stub.c" --gen_device_file_name "tmp/CMakeCUDACompilerId.cudafe1.gpu"  "tmp/CMakeCUDACompilerId.cpp1.ii" -o "tmp/CMakeCUDACompilerId.ptx"]
  considering line: [ptxas -arch=sm_80 -m64  "tmp/CMakeCUDACompilerId.ptx"  -o "tmp/CMakeCUDACompilerId.cubin" ]
  considering line: [fatbinary --create="tmp/CMakeCUDACompilerId.fatbin" -64 --cicc-cmdline="-ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 " "--image3=kind=elf,sm=80,file=tmp/CMakeCUDACompilerId.cubin" --embedded-fatbin="tmp/CMakeCUDACompilerId.fatbin.c" ]
  considering line: [gcc -D__CUDA_ARCH__=800 -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -c -x c++  -DCUDA_DOUBLE_MATH_FUNCTIONS -Wno-psabi "-I/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include"   -m64 "tmp/CMakeCUDACompilerId.cudafe1.cpp" -o "tmp/CMakeCUDACompilerId.o" ]
  considering line: [nvlink -m64 --arch=sm_80 --register-link-binaries="tmp/a_dlink.reg.c"    "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs" "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib" -cpu-arch=X86_64 "tmp/CMakeCUDACompilerId.o"  -lcudadevrt  -o "tmp/a_dlink.cubin" --host-ccbin "gcc"]
    ignoring nvlink line
  considering line: [fatbinary --create="tmp/a_dlink.fatbin" -64 --cicc-cmdline="-ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 " -link "--image3=kind=elf,sm=80,file=tmp/a_dlink.cubin" --embedded-fatbin="tmp/a_dlink.fatbin.c" ]
  considering line: [gcc -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -c -x c++ -DFATBINFILE="\"tmp/a_dlink.fatbin.c\"" -DREGISTERLINKBINARYFILE="\"tmp/a_dlink.reg.c\"" -I. -D__NV_EXTRA_INITIALIZATION= -D__NV_EXTRA_FINALIZATION= -D__CUDA_INCLUDE_COMPILER_INTERNAL_HEADERS__  -Wno-psabi "-I/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include"    -D__CUDACC_VER_MAJOR__=12 -D__CUDACC_VER_MINOR__=4 -D__CUDACC_VER_BUILD__=131 -D__CUDA_API_VER_MAJOR__=12 -D__CUDA_API_VER_MINOR__=4 -D__NVCC_DIAG_PRAGMA_SUPPORT__=1 -m64 "/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/crt/link.stub" -o "tmp/a_dlink.o" ]
  considering line: [g++ -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -m64 -Wl,--start-group "tmp/a_dlink.o" "tmp/CMakeCUDACompilerId.o"   "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs" "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib"  -lcudadevrt  -lcudart_static  -lrt -lpthread  -ldl  -Wl,--end-group -o "a.out" ]
    extracted link line: [g++ -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -m64 -Wl,--start-group "tmp/a_dlink.o" "tmp/CMakeCUDACompilerId.o"   "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs" "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib"  -lcudadevrt  -lcudart_static  -lrt -lpthread  -ldl  -Wl,--end-group -o "a.out" ]
  considering line: []
  extracted link launcher name: [g++]
  found link launcher absolute path: [/opt/cray/pe/gcc-native/13/bin/g++]

  link line regex: [^( *|.*[/\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  link line: [cuda-fake-ld g++ -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -m64 -Wl,--start-group "tmp/a_dlink.o" "tmp/CMakeCUDACompilerId.o"   "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs" "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib"  -lcudadevrt  -lcudart_static  -lrt -lpthread  -ldl  -Wl,--end-group -o "a.out" ]
    arg [cuda-fake-ld] ==> ignore
    arg [g++] ==> ignore
    arg [-D__CUDA_ARCH_LIST__=800] ==> ignore
    arg [-D__NV_LEGACY_LAUNCH] ==> ignore
    arg [-m64] ==> ignore
    arg [-Wl,--start-group] ==> ignore
    arg [tmp/a_dlink.o] ==> ignore
    arg [tmp/CMakeCUDACompilerId.o] ==> ignore
    arg [-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs] ==> dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs]
    arg [-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib] ==> dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib]
    arg [-lcudadevrt] ==> lib [cudadevrt]
    arg [-lcudart_static] ==> lib [cudart_static]
    arg [-lrt] ==> lib [rt]
    arg [-lpthread] ==> lib [pthread]
    arg [-ldl] ==> lib [dl]
    arg [-Wl,--end-group] ==> ignore
    arg [-o] ==> ignore
    arg [a.out] ==> ignore
  collapse library dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs] ==> [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs]
  collapse library dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib] ==> [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib]
  implicit libs: [cudadevrt;cudart_static;rt;pthread;dl]
  implicit objs: []
  implicit dirs: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs;/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib]
  implicit fwks: []


Parsed CUDA nvcc include information from above output:
  found 'PATH=' string: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../nvvm/bin:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin:/global/homes/j/jackyan/workspace/spack/bin:/opt/nersc/pe/bin:/global/common/software/nersc/bin:/global/common/software/nersc9/darshan/default/bin:/global/common/software/nersc9/sqs/2.0/bin:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/compute-sanitizer:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/libnvvp:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/profilers/Nsight_Compute:/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/profilers/Nsight_Systems/bin:/opt/cray/pe/perftools/24.07.0/bin:/opt/cray/pe/papi/7.1.0.2/bin:/opt/cray/pe/gcc-native/13/bin:/opt/cray/pe/craype/2.7.32/bin:/opt/cray/pe/mpich/8.1.30/ofi/gnu/12.3/bin:/opt/cray/pe/mpich/8.1.30/bin:/opt/cray/libfabric/1.20.1/bin:/usr/local/bin:/usr/bin:/bin:/usr/lib/mit/bin:/opt/cray/pe/bin]
  found 'LIBRARIES=' string: ["-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs" "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib"]
  considering line: [#$ rm tmp/a_dlink.reg.c]
  considering line: [gcc -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -E -x c++ -D__CUDACC__ -D__NVCC__  "-I/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include"    -D__CUDACC_VER_MAJOR__=12 -D__CUDACC_VER_MINOR__=4 -D__CUDACC_VER_BUILD__=131 -D__CUDA_API_VER_MAJOR__=12 -D__CUDA_API_VER_MINOR__=4 -D__NVCC_DIAG_PRAGMA_SUPPORT__=1 -include "cuda_runtime.h" -m64 "CMakeCUDACompilerId.cu" -o "tmp/CMakeCUDACompilerId.cpp4.ii" ]
  considering line: [cudafe++ --c++17 --gnu_version=130201 --display_error_number --orig_src_file_name "CMakeCUDACompilerId.cu" --orig_src_path_name "/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/3.20.4/CompilerIdCUDA/CMakeCUDACompilerId.cu" --allow_managed  --m64 --parse_templates --gen_c_file_name "tmp/CMakeCUDACompilerId.cudafe1.cpp" --stub_file_name "CMakeCUDACompilerId.cudafe1.stub.c" --gen_module_id_file --module_id_file_name "tmp/CMakeCUDACompilerId.module_id" "tmp/CMakeCUDACompilerId.cpp4.ii" ]
  considering line: [gcc -D__CUDA_ARCH__=800 -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -E -x c++  -DCUDA_DOUBLE_MATH_FUNCTIONS -D__CUDACC__ -D__NVCC__  "-I/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include"    -D__CUDACC_VER_MAJOR__=12 -D__CUDACC_VER_MINOR__=4 -D__CUDACC_VER_BUILD__=131 -D__CUDA_API_VER_MAJOR__=12 -D__CUDA_API_VER_MINOR__=4 -D__NVCC_DIAG_PRAGMA_SUPPORT__=1 -include "cuda_runtime.h" -m64 "CMakeCUDACompilerId.cu" -o "tmp/CMakeCUDACompilerId.cpp1.ii" ]
  considering line: [cicc --c++17 --gnu_version=130201 --display_error_number --orig_src_file_name "CMakeCUDACompilerId.cu" --orig_src_path_name "/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/3.20.4/CompilerIdCUDA/CMakeCUDACompilerId.cu" --allow_managed   -arch compute_80 -m64 --no-version-ident -ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 --include_file_name "CMakeCUDACompilerId.fatbin.c" -tused --module_id_file_name "tmp/CMakeCUDACompilerId.module_id" --gen_c_file_name "tmp/CMakeCUDACompilerId.cudafe1.c" --stub_file_name "tmp/CMakeCUDACompilerId.cudafe1.stub.c" --gen_device_file_name "tmp/CMakeCUDACompilerId.cudafe1.gpu"  "tmp/CMakeCUDACompilerId.cpp1.ii" -o "tmp/CMakeCUDACompilerId.ptx"]
  considering line: [ptxas -arch=sm_80 -m64  "tmp/CMakeCUDACompilerId.ptx"  -o "tmp/CMakeCUDACompilerId.cubin" ]
  considering line: [fatbinary --create="tmp/CMakeCUDACompilerId.fatbin" -64 --cicc-cmdline="-ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 " "--image3=kind=elf,sm=80,file=tmp/CMakeCUDACompilerId.cubin" --embedded-fatbin="tmp/CMakeCUDACompilerId.fatbin.c" ]
  considering line: [gcc -D__CUDA_ARCH__=800 -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -c -x c++  -DCUDA_DOUBLE_MATH_FUNCTIONS -Wno-psabi "-I/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include"   -m64 "tmp/CMakeCUDACompilerId.cudafe1.cpp" -o "tmp/CMakeCUDACompilerId.o" ]
  considering line: [nvlink -m64 --arch=sm_80 --register-link-binaries="tmp/a_dlink.reg.c"    "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs" "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib" -cpu-arch=X86_64 "tmp/CMakeCUDACompilerId.o"  -lcudadevrt  -o "tmp/a_dlink.cubin" --host-ccbin "gcc"]
    ignoring nvlink line
  considering line: [fatbinary --create="tmp/a_dlink.fatbin" -64 --cicc-cmdline="-ftz=0 -prec_div=1 -prec_sqrt=1 -fmad=1 " -link "--image3=kind=elf,sm=80,file=tmp/a_dlink.cubin" --embedded-fatbin="tmp/a_dlink.fatbin.c" ]
  considering line: [gcc -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -c -x c++ -DFATBINFILE="\"tmp/a_dlink.fatbin.c\"" -DREGISTERLINKBINARYFILE="\"tmp/a_dlink.reg.c\"" -I. -D__NV_EXTRA_INITIALIZATION= -D__NV_EXTRA_FINALIZATION= -D__CUDA_INCLUDE_COMPILER_INTERNAL_HEADERS__  -Wno-psabi "-I/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include"    -D__CUDACC_VER_MAJOR__=12 -D__CUDACC_VER_MINOR__=4 -D__CUDACC_VER_BUILD__=131 -D__CUDA_API_VER_MAJOR__=12 -D__CUDA_API_VER_MINOR__=4 -D__NVCC_DIAG_PRAGMA_SUPPORT__=1 -m64 "/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/crt/link.stub" -o "tmp/a_dlink.o" ]
  considering line: [g++ -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -m64 -Wl,--start-group "tmp/a_dlink.o" "tmp/CMakeCUDACompilerId.o"   "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs" "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib"  -lcudadevrt  -lcudart_static  -lrt -lpthread  -ldl  -Wl,--end-group -o "a.out" ]
    extracted link line: [g++ -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -m64 -Wl,--start-group "tmp/a_dlink.o" "tmp/CMakeCUDACompilerId.o"   "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs" "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib"  -lcudadevrt  -lcudart_static  -lrt -lpthread  -ldl  -Wl,--end-group -o "a.out" ]
  considering line: []
  extracted link launcher name: [g++]
  found link launcher absolute path: [/opt/cray/pe/gcc-native/13/bin/g++]
  found 'INCLUDES=' string: ["-I/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include"  ]

  link line regex: [^( *|.*[/\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  link line: [cuda-fake-ld g++ -D__CUDA_ARCH_LIST__=800 -D__NV_LEGACY_LAUNCH -m64 -Wl,--start-group "tmp/a_dlink.o" "tmp/CMakeCUDACompilerId.o"   "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs" "-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib"  -lcudadevrt  -lcudart_static  -lrt -lpthread  -ldl  -Wl,--end-group -o "a.out" ]
    arg [cuda-fake-ld] ==> ignore
    arg [g++] ==> ignore
    arg [-D__CUDA_ARCH_LIST__=800] ==> ignore
    arg [-D__NV_LEGACY_LAUNCH] ==> ignore
    arg [-m64] ==> ignore
    arg [-Wl,--start-group] ==> ignore
    arg [tmp/a_dlink.o] ==> ignore
    arg [tmp/CMakeCUDACompilerId.o] ==> ignore
    arg [-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs] ==> dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs]
    arg [-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib] ==> dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib]
    arg [-lcudadevrt] ==> lib [cudadevrt]
    arg [-lcudart_static] ==> lib [cudart_static]
    arg [-lrt] ==> lib [rt]
    arg [-lpthread] ==> lib [pthread]
    arg [-ldl] ==> lib [dl]
    arg [-Wl,--end-group] ==> ignore
    arg [-o] ==> ignore
    arg [a.out] ==> ignore
  collapse library dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib/stubs] ==> [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs]
  collapse library dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/lib] ==> [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib]
  implicit libs: [cudadevrt;cudart_static;rt;pthread;dl]
  implicit objs: []
  implicit dirs: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs;/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib]
  implicit fwks: []


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: /usr/bin/c++ 
Build flags: 
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"

The CXX compiler identification is GNU, found in "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/3.20.4/CompilerIdCXX/a.out"

Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: /usr/bin/cc 
Build flags: 
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"

The C compiler identification is GNU, found in "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/3.20.4/CompilerIdC/a.out"

Detecting CUDA compiler ABI info compiled with the following output:
Change Dir: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_8b833/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_8b833.dir/build.make CMakeFiles/cmTC_8b833.dir/build
gmake[1]: Entering directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'
Building CUDA object CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o
/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/nvcc -forward-unknown-to-host-compiler   --generate-code=arch=compute_80,code=[compute_80,sm_80]   -Xcompiler=-v -MD -MT CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o -MF CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o.d -x cu -c /usr/share/cmake/Modules/CMakeCUDACompilerABI.cu -o CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o
Using built-in specs.
COLLECT_GCC=gcc
OFFLOAD_TARGET_NAMES=nvptx-none
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-suse-linux
Configured with: ../configure CFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' CXXFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' XCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' TCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' GDCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c,c++,objc,fortran,obj-c++,ada,go,d,m2 --enable-offload-targets=nvptx-none, --enable-offload-defaulted --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/13 --with-libstdcxx-zoneinfo=/usr/share/zoneinfo --enable-ssp --disable-libssp --disable-libvtv --enable-cet=auto --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-libphobos --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-13 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --enable-link-serialization --build=x86_64-suse-linux --host=x86_64-suse-linux
Thread model: posix
Supported LTO compression algorithms: zlib
gcc version 13.2.1 20240206 [revision 67ac78caf31f7cb3202177e6428a46d829b70f23] (SUSE Linux) 
COLLECT_GCC_OPTIONS='-D' '__CUDA_ARCH__=800' '-D' '__CUDA_ARCH_LIST__=800' '-D' '__NV_LEGACY_LAUNCH' '-E' '-D' 'CUDA_DOUBLE_MATH_FUNCTIONS' '-D' '__CUDACC__' '-D' '__NVCC__' '-v' '-I' '/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include' '-D' '__CUDACC_VER_MAJOR__=12' '-D' '__CUDACC_VER_MINOR__=4' '-D' '__CUDACC_VER_BUILD__=131' '-D' '__CUDA_API_VER_MAJOR__=12' '-D' '__CUDA_API_VER_MINOR__=4' '-D' '__NVCC_DIAG_PRAGMA_SUPPORT__=1' '-include' 'cuda_runtime.h' '-m64' '-o' '/tmp/tmpxft_0015f755_00000000-7_CMakeCUDACompilerABI.cpp1.ii' '-mtune=generic' '-march=x86-64' '-dumpdir' '/tmp/'
 /usr/lib64/gcc/x86_64-suse-linux/13/cc1plus -E -quiet -v -I /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include -D_GNU_SOURCE -D __CUDA_ARCH__=800 -D __CUDA_ARCH_LIST__=800 -D __NV_LEGACY_LAUNCH -D CUDA_DOUBLE_MATH_FUNCTIONS -D __CUDACC__ -D __NVCC__ -D __CUDACC_VER_MAJOR__=12 -D __CUDACC_VER_MINOR__=4 -D __CUDACC_VER_BUILD__=131 -D __CUDA_API_VER_MAJOR__=12 -D __CUDA_API_VER_MINOR__=4 -D __NVCC_DIAG_PRAGMA_SUPPORT__=1 -include cuda_runtime.h /usr/share/cmake/Modules/CMakeCUDACompilerABI.cu -o /tmp/tmpxft_0015f755_00000000-7_CMakeCUDACompilerABI.cpp1.ii -m64 -mtune=generic -march=x86-64 -dumpdir /tmp/ -dumpbase tmpxft_0015f755_00000000-7_CMakeCUDACompilerABI.cpp1.cu -dumpbase-ext .cu
ignoring duplicate directory "/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include"
#include "..." search starts here:
#include <...> search starts here:
 /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include
 /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include
 /usr/include/c++/13
 /usr/include/c++/13/x86_64-suse-linux
 /usr/include/c++/13/backward
 /usr/lib64/gcc/x86_64-suse-linux/13/include
 /usr/local/include
 /usr/lib64/gcc/x86_64-suse-linux/13/include-fixed
 /usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/include
 /usr/include
End of search list.
COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/bin/
LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-D' '__CUDA_ARCH__=800' '-D' '__CUDA_ARCH_LIST__=800' '-D' '__NV_LEGACY_LAUNCH' '-E' '-D' 'CUDA_DOUBLE_MATH_FUNCTIONS' '-D' '__CUDACC__' '-D' '__NVCC__' '-v' '-I' '/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include' '-D' '__CUDACC_VER_MAJOR__=12' '-D' '__CUDACC_VER_MINOR__=4' '-D' '__CUDACC_VER_BUILD__=131' '-D' '__CUDA_API_VER_MAJOR__=12' '-D' '__CUDA_API_VER_MINOR__=4' '-D' '__NVCC_DIAG_PRAGMA_SUPPORT__=1' '-include' 'cuda_runtime.h' '-m64' '-o' '/tmp/tmpxft_0015f755_00000000-7_CMakeCUDACompilerABI.cpp1.ii' '-mtune=generic' '-march=x86-64' '-dumpdir' '/tmp/tmpxft_0015f755_00000000-7_CMakeCUDACompilerABI.cpp1.'
Using built-in specs.
COLLECT_GCC=gcc
OFFLOAD_TARGET_NAMES=nvptx-none
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-suse-linux
Configured with: ../configure CFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' CXXFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' XCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' TCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' GDCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c,c++,objc,fortran,obj-c++,ada,go,d,m2 --enable-offload-targets=nvptx-none, --enable-offload-defaulted --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/13 --with-libstdcxx-zoneinfo=/usr/share/zoneinfo --enable-ssp --disable-libssp --disable-libvtv --enable-cet=auto --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-libphobos --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-13 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --enable-link-serialization --build=x86_64-suse-linux --host=x86_64-suse-linux
Thread model: posix
Supported LTO compression algorithms: zlib
gcc version 13.2.1 20240206 [revision 67ac78caf31f7cb3202177e6428a46d829b70f23] (SUSE Linux) 
COLLECT_GCC_OPTIONS='-D' '__CUDA_ARCH_LIST__=800' '-D' '__NV_LEGACY_LAUNCH' '-E' '-D' '__CUDACC__' '-D' '__NVCC__' '-v' '-I' '/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include' '-D' '__CUDACC_VER_MAJOR__=12' '-D' '__CUDACC_VER_MINOR__=4' '-D' '__CUDACC_VER_BUILD__=131' '-D' '__CUDA_API_VER_MAJOR__=12' '-D' '__CUDA_API_VER_MINOR__=4' '-D' '__NVCC_DIAG_PRAGMA_SUPPORT__=1' '-include' 'cuda_runtime.h' '-m64' '-o' '/tmp/tmpxft_0015f755_00000000-5_CMakeCUDACompilerABI.cpp4.ii' '-mtune=generic' '-march=x86-64' '-dumpdir' '/tmp/'
 /usr/lib64/gcc/x86_64-suse-linux/13/cc1plus -E -quiet -v -I /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include -D_GNU_SOURCE -D __CUDA_ARCH_LIST__=800 -D __NV_LEGACY_LAUNCH -D __CUDACC__ -D __NVCC__ -D __CUDACC_VER_MAJOR__=12 -D __CUDACC_VER_MINOR__=4 -D __CUDACC_VER_BUILD__=131 -D __CUDA_API_VER_MAJOR__=12 -D __CUDA_API_VER_MINOR__=4 -D __NVCC_DIAG_PRAGMA_SUPPORT__=1 -include cuda_runtime.h /usr/share/cmake/Modules/CMakeCUDACompilerABI.cu -o /tmp/tmpxft_0015f755_00000000-5_CMakeCUDACompilerABI.cpp4.ii -m64 -mtune=generic -march=x86-64 -dumpdir /tmp/ -dumpbase tmpxft_0015f755_00000000-5_CMakeCUDACompilerABI.cpp4.cu -dumpbase-ext .cu
ignoring duplicate directory "/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include"
#include "..." search starts here:
#include <...> search starts here:
 /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include
 /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include
 /usr/include/c++/13
 /usr/include/c++/13/x86_64-suse-linux
 /usr/include/c++/13/backward
 /usr/lib64/gcc/x86_64-suse-linux/13/include
 /usr/local/include
 /usr/lib64/gcc/x86_64-suse-linux/13/include-fixed
 /usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/include
 /usr/include
End of search list.
COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/bin/
LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-D' '__CUDA_ARCH_LIST__=800' '-D' '__NV_LEGACY_LAUNCH' '-E' '-D' '__CUDACC__' '-D' '__NVCC__' '-v' '-I' '/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include' '-D' '__CUDACC_VER_MAJOR__=12' '-D' '__CUDACC_VER_MINOR__=4' '-D' '__CUDACC_VER_BUILD__=131' '-D' '__CUDA_API_VER_MAJOR__=12' '-D' '__CUDA_API_VER_MINOR__=4' '-D' '__NVCC_DIAG_PRAGMA_SUPPORT__=1' '-include' 'cuda_runtime.h' '-m64' '-o' '/tmp/tmpxft_0015f755_00000000-5_CMakeCUDACompilerABI.cpp4.ii' '-mtune=generic' '-march=x86-64' '-dumpdir' '/tmp/tmpxft_0015f755_00000000-5_CMakeCUDACompilerABI.cpp4.'
Using built-in specs.
COLLECT_GCC=gcc
OFFLOAD_TARGET_NAMES=nvptx-none
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-suse-linux
Configured with: ../configure CFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' CXXFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' XCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' TCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' GDCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c,c++,objc,fortran,obj-c++,ada,go,d,m2 --enable-offload-targets=nvptx-none, --enable-offload-defaulted --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/13 --with-libstdcxx-zoneinfo=/usr/share/zoneinfo --enable-ssp --disable-libssp --disable-libvtv --enable-cet=auto --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-libphobos --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-13 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --enable-link-serialization --build=x86_64-suse-linux --host=x86_64-suse-linux
Thread model: posix
Supported LTO compression algorithms: zlib
gcc version 13.2.1 20240206 [revision 67ac78caf31f7cb3202177e6428a46d829b70f23] (SUSE Linux) 
COLLECT_GCC_OPTIONS='-D' '__CUDA_ARCH__=800' '-D' '__CUDA_ARCH_LIST__=800' '-D' '__NV_LEGACY_LAUNCH' '-c' '-D' 'CUDA_DOUBLE_MATH_FUNCTIONS' '-v' '-Wno-psabi' '-I' '/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include' '-m64' '-o' 'CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_8b833.dir/'
 /usr/lib64/gcc/x86_64-suse-linux/13/cc1plus -quiet -v -I /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include -D_GNU_SOURCE -D __CUDA_ARCH__=800 -D __CUDA_ARCH_LIST__=800 -D __NV_LEGACY_LAUNCH -D CUDA_DOUBLE_MATH_FUNCTIONS /tmp/tmpxft_0015f755_00000000-6_CMakeCUDACompilerABI.cudafe1.cpp -quiet -dumpdir CMakeFiles/cmTC_8b833.dir/ -dumpbase CMakeCUDACompilerABI.cu.cpp -dumpbase-ext .cpp -m64 -mtune=generic -march=x86-64 -Wno-psabi -version -o /tmp/ccIsNtP0.s
GNU C++17 (SUSE Linux) version 13.2.1 20240206 [revision 67ac78caf31f7cb3202177e6428a46d829b70f23] (x86_64-suse-linux)
	compiled by GNU C version 13.2.1 20240206 [revision 67ac78caf31f7cb3202177e6428a46d829b70f23], GMP version 6.1.2, MPFR version 4.0.2-p6, MPC version 1.1.0, isl version isl-0.18-GMP

GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
ignoring duplicate directory "/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include"
#include "..." search starts here:
#include <...> search starts here:
 /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include
 /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include
 /usr/include/c++/13
 /usr/include/c++/13/x86_64-suse-linux
 /usr/include/c++/13/backward
 /usr/lib64/gcc/x86_64-suse-linux/13/include
 /usr/local/include
 /usr/lib64/gcc/x86_64-suse-linux/13/include-fixed
 /usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/include
 /usr/include
End of search list.
Compiler executable checksum: 00000000000000000000000000000000
COLLECT_GCC_OPTIONS='-D' '__CUDA_ARCH__=800' '-D' '__CUDA_ARCH_LIST__=800' '-D' '__NV_LEGACY_LAUNCH' '-c' '-D' 'CUDA_DOUBLE_MATH_FUNCTIONS' '-v' '-Wno-psabi' '-I' '/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include' '-m64' '-o' 'CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_8b833.dir/'
 /usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/bin/as -v -I /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include --64 -o CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o /tmp/ccIsNtP0.s
GNU assembler version 2.43.1 (x86_64-suse-linux) using BFD version (GNU Binutils; SUSE Linux Enterprise 15) 2.43.1.20241209-150100.7.52
COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/bin/
LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-D' '__CUDA_ARCH__=800' '-D' '__CUDA_ARCH_LIST__=800' '-D' '__NV_LEGACY_LAUNCH' '-c' '-D' 'CUDA_DOUBLE_MATH_FUNCTIONS' '-v' '-Wno-psabi' '-I' '/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include' '-m64' '-o' 'CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.'
Linking CUDA executable cmTC_8b833
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_8b833.dir/link.txt --verbose=1
/opt/cray/pe/gcc-native/13/bin/g++  -v CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o -o cmTC_8b833  -lcudadevrt -lcudart_static -lrt -lpthread -ldl  -L"/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs" -L"/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib"
Using built-in specs.
COLLECT_GCC=/opt/cray/pe/gcc-native/13/bin/g++
COLLECT_LTO_WRAPPER=/usr/lib64/gcc/x86_64-suse-linux/13/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-suse-linux
Configured with: ../configure CFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' CXXFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' XCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' TCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' GDCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c,c++,objc,fortran,obj-c++,ada,go,d,m2 --enable-offload-targets=nvptx-none, --enable-offload-defaulted --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/13 --with-libstdcxx-zoneinfo=/usr/share/zoneinfo --enable-ssp --disable-libssp --disable-libvtv --enable-cet=auto --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-libphobos --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-13 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --enable-link-serialization --build=x86_64-suse-linux --host=x86_64-suse-linux
Thread model: posix
Supported LTO compression algorithms: zlib
gcc version 13.2.1 20240206 [revision 67ac78caf31f7cb3202177e6428a46d829b70f23] (SUSE Linux) 
COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/bin/
LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_8b833' '-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs' '-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_8b833.'
 /usr/lib64/gcc/x86_64-suse-linux/13/collect2 -plugin /usr/lib64/gcc/x86_64-suse-linux/13/liblto_plugin.so -plugin-opt=/usr/lib64/gcc/x86_64-suse-linux/13/lto-wrapper -plugin-opt=-fresolution=/tmp/cccw2Gcq.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_8b833 /usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crt1.o /usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crti.o /usr/lib64/gcc/x86_64-suse-linux/13/crtbegin.o -L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs -L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib -L/usr/lib64/gcc/x86_64-suse-linux/13 -L/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/lib -L/usr/lib64/gcc/x86_64-suse-linux/13/../../.. CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o -lcudadevrt -lcudart_static -lrt -lpthread -ldl -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib64/gcc/x86_64-suse-linux/13/crtend.o /usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crtn.o
COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_8b833' '-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs' '-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_8b833.'
gmake[1]: Leaving directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'



Parsed CUDA implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include]
    add: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include]
    add: [/usr/include/c++/13]
    add: [/usr/include/c++/13/x86_64-suse-linux]
    add: [/usr/include/c++/13/backward]
    add: [/usr/lib64/gcc/x86_64-suse-linux/13/include]
    add: [/usr/local/include]
    add: [/usr/lib64/gcc/x86_64-suse-linux/13/include-fixed]
    add: [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/include]
    add: [/usr/include]
  end of search list found
  collapse include dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include] ==> [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/include]
  collapse include dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include] ==> [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include]
  collapse include dir [/usr/include/c++/13] ==> [/usr/include/c++/13]
  collapse include dir [/usr/include/c++/13/x86_64-suse-linux] ==> [/usr/include/c++/13/x86_64-suse-linux]
  collapse include dir [/usr/include/c++/13/backward] ==> [/usr/include/c++/13/backward]
  collapse include dir [/usr/lib64/gcc/x86_64-suse-linux/13/include] ==> [/usr/lib64/gcc/x86_64-suse-linux/13/include]
  collapse include dir [/usr/local/include] ==> [/usr/local/include]
  collapse include dir [/usr/lib64/gcc/x86_64-suse-linux/13/include-fixed] ==> [/usr/lib64/gcc/x86_64-suse-linux/13/include-fixed]
  collapse include dir [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/include] ==> [/usr/x86_64-suse-linux/include]
  collapse include dir [/usr/include] ==> [/usr/include]
  implicit include dirs: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/include;/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include;/usr/include/c++/13;/usr/include/c++/13/x86_64-suse-linux;/usr/include/c++/13/backward;/usr/lib64/gcc/x86_64-suse-linux/13/include;/usr/local/include;/usr/lib64/gcc/x86_64-suse-linux/13/include-fixed;/usr/x86_64-suse-linux/include;/usr/include]


Parsed CUDA implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_8b833/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_8b833.dir/build.make CMakeFiles/cmTC_8b833.dir/build]
  ignore line: [gmake[1]: Entering directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp']
  ignore line: [Building CUDA object CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o]
  ignore line: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/nvcc -forward-unknown-to-host-compiler   --generate-code=arch=compute_80 code=[compute_80 sm_80]   -Xcompiler=-v -MD -MT CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o -MF CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o.d -x cu -c /usr/share/cmake/Modules/CMakeCUDACompilerABI.cu -o CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=gcc]
  ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
  ignore line: [OFFLOAD_TARGET_DEFAULT=1]
  ignore line: [Target: x86_64-suse-linux]
  ignore line: [Configured with: ../configure CFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' CXXFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' XCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' TCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' GDCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c c++ objc fortran obj-c++ ada go d m2 --enable-offload-targets=nvptx-none  --enable-offload-defaulted --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/13 --with-libstdcxx-zoneinfo=/usr/share/zoneinfo --enable-ssp --disable-libssp --disable-libvtv --enable-cet=auto --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-libphobos --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-13 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --enable-link-serialization --build=x86_64-suse-linux --host=x86_64-suse-linux]
  ignore line: [Thread model: posix]
  ignore line: [Supported LTO compression algorithms: zlib]
  ignore line: [gcc version 13.2.1 20240206 [revision 67ac78caf31f7cb3202177e6428a46d829b70f23] (SUSE Linux) ]
  ignore line: [COLLECT_GCC_OPTIONS='-D' '__CUDA_ARCH__=800' '-D' '__CUDA_ARCH_LIST__=800' '-D' '__NV_LEGACY_LAUNCH' '-E' '-D' 'CUDA_DOUBLE_MATH_FUNCTIONS' '-D' '__CUDACC__' '-D' '__NVCC__' '-v' '-I' '/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include' '-D' '__CUDACC_VER_MAJOR__=12' '-D' '__CUDACC_VER_MINOR__=4' '-D' '__CUDACC_VER_BUILD__=131' '-D' '__CUDA_API_VER_MAJOR__=12' '-D' '__CUDA_API_VER_MINOR__=4' '-D' '__NVCC_DIAG_PRAGMA_SUPPORT__=1' '-include' 'cuda_runtime.h' '-m64' '-o' '/tmp/tmpxft_0015f755_00000000-7_CMakeCUDACompilerABI.cpp1.ii' '-mtune=generic' '-march=x86-64' '-dumpdir' '/tmp/']
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/13/cc1plus -E -quiet -v -I /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include -D_GNU_SOURCE -D __CUDA_ARCH__=800 -D __CUDA_ARCH_LIST__=800 -D __NV_LEGACY_LAUNCH -D CUDA_DOUBLE_MATH_FUNCTIONS -D __CUDACC__ -D __NVCC__ -D __CUDACC_VER_MAJOR__=12 -D __CUDACC_VER_MINOR__=4 -D __CUDACC_VER_BUILD__=131 -D __CUDA_API_VER_MAJOR__=12 -D __CUDA_API_VER_MINOR__=4 -D __NVCC_DIAG_PRAGMA_SUPPORT__=1 -include cuda_runtime.h /usr/share/cmake/Modules/CMakeCUDACompilerABI.cu -o /tmp/tmpxft_0015f755_00000000-7_CMakeCUDACompilerABI.cpp1.ii -m64 -mtune=generic -march=x86-64 -dumpdir /tmp/ -dumpbase tmpxft_0015f755_00000000-7_CMakeCUDACompilerABI.cpp1.cu -dumpbase-ext .cu]
  ignore line: [ignoring duplicate directory "/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include]
  ignore line: [ /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include]
  ignore line: [ /usr/include/c++/13]
  ignore line: [ /usr/include/c++/13/x86_64-suse-linux]
  ignore line: [ /usr/include/c++/13/backward]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/13/include]
  ignore line: [ /usr/local/include]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/13/include-fixed]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/include]
  ignore line: [ /usr/include]
  ignore line: [End of search list.]
  ignore line: [COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/bin/]
  ignore line: [LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-D' '__CUDA_ARCH__=800' '-D' '__CUDA_ARCH_LIST__=800' '-D' '__NV_LEGACY_LAUNCH' '-E' '-D' 'CUDA_DOUBLE_MATH_FUNCTIONS' '-D' '__CUDACC__' '-D' '__NVCC__' '-v' '-I' '/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include' '-D' '__CUDACC_VER_MAJOR__=12' '-D' '__CUDACC_VER_MINOR__=4' '-D' '__CUDACC_VER_BUILD__=131' '-D' '__CUDA_API_VER_MAJOR__=12' '-D' '__CUDA_API_VER_MINOR__=4' '-D' '__NVCC_DIAG_PRAGMA_SUPPORT__=1' '-include' 'cuda_runtime.h' '-m64' '-o' '/tmp/tmpxft_0015f755_00000000-7_CMakeCUDACompilerABI.cpp1.ii' '-mtune=generic' '-march=x86-64' '-dumpdir' '/tmp/tmpxft_0015f755_00000000-7_CMakeCUDACompilerABI.cpp1.']
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=gcc]
  ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
  ignore line: [OFFLOAD_TARGET_DEFAULT=1]
  ignore line: [Target: x86_64-suse-linux]
  ignore line: [Configured with: ../configure CFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' CXXFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' XCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' TCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' GDCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c c++ objc fortran obj-c++ ada go d m2 --enable-offload-targets=nvptx-none  --enable-offload-defaulted --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/13 --with-libstdcxx-zoneinfo=/usr/share/zoneinfo --enable-ssp --disable-libssp --disable-libvtv --enable-cet=auto --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-libphobos --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-13 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --enable-link-serialization --build=x86_64-suse-linux --host=x86_64-suse-linux]
  ignore line: [Thread model: posix]
  ignore line: [Supported LTO compression algorithms: zlib]
  ignore line: [gcc version 13.2.1 20240206 [revision 67ac78caf31f7cb3202177e6428a46d829b70f23] (SUSE Linux) ]
  ignore line: [COLLECT_GCC_OPTIONS='-D' '__CUDA_ARCH_LIST__=800' '-D' '__NV_LEGACY_LAUNCH' '-E' '-D' '__CUDACC__' '-D' '__NVCC__' '-v' '-I' '/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include' '-D' '__CUDACC_VER_MAJOR__=12' '-D' '__CUDACC_VER_MINOR__=4' '-D' '__CUDACC_VER_BUILD__=131' '-D' '__CUDA_API_VER_MAJOR__=12' '-D' '__CUDA_API_VER_MINOR__=4' '-D' '__NVCC_DIAG_PRAGMA_SUPPORT__=1' '-include' 'cuda_runtime.h' '-m64' '-o' '/tmp/tmpxft_0015f755_00000000-5_CMakeCUDACompilerABI.cpp4.ii' '-mtune=generic' '-march=x86-64' '-dumpdir' '/tmp/']
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/13/cc1plus -E -quiet -v -I /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include -D_GNU_SOURCE -D __CUDA_ARCH_LIST__=800 -D __NV_LEGACY_LAUNCH -D __CUDACC__ -D __NVCC__ -D __CUDACC_VER_MAJOR__=12 -D __CUDACC_VER_MINOR__=4 -D __CUDACC_VER_BUILD__=131 -D __CUDA_API_VER_MAJOR__=12 -D __CUDA_API_VER_MINOR__=4 -D __NVCC_DIAG_PRAGMA_SUPPORT__=1 -include cuda_runtime.h /usr/share/cmake/Modules/CMakeCUDACompilerABI.cu -o /tmp/tmpxft_0015f755_00000000-5_CMakeCUDACompilerABI.cpp4.ii -m64 -mtune=generic -march=x86-64 -dumpdir /tmp/ -dumpbase tmpxft_0015f755_00000000-5_CMakeCUDACompilerABI.cpp4.cu -dumpbase-ext .cu]
  ignore line: [ignoring duplicate directory "/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include]
  ignore line: [ /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include]
  ignore line: [ /usr/include/c++/13]
  ignore line: [ /usr/include/c++/13/x86_64-suse-linux]
  ignore line: [ /usr/include/c++/13/backward]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/13/include]
  ignore line: [ /usr/local/include]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/13/include-fixed]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/include]
  ignore line: [ /usr/include]
  ignore line: [End of search list.]
  ignore line: [COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/bin/]
  ignore line: [LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-D' '__CUDA_ARCH_LIST__=800' '-D' '__NV_LEGACY_LAUNCH' '-E' '-D' '__CUDACC__' '-D' '__NVCC__' '-v' '-I' '/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include' '-D' '__CUDACC_VER_MAJOR__=12' '-D' '__CUDACC_VER_MINOR__=4' '-D' '__CUDACC_VER_BUILD__=131' '-D' '__CUDA_API_VER_MAJOR__=12' '-D' '__CUDA_API_VER_MINOR__=4' '-D' '__NVCC_DIAG_PRAGMA_SUPPORT__=1' '-include' 'cuda_runtime.h' '-m64' '-o' '/tmp/tmpxft_0015f755_00000000-5_CMakeCUDACompilerABI.cpp4.ii' '-mtune=generic' '-march=x86-64' '-dumpdir' '/tmp/tmpxft_0015f755_00000000-5_CMakeCUDACompilerABI.cpp4.']
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=gcc]
  ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
  ignore line: [OFFLOAD_TARGET_DEFAULT=1]
  ignore line: [Target: x86_64-suse-linux]
  ignore line: [Configured with: ../configure CFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' CXXFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' XCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' TCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' GDCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c c++ objc fortran obj-c++ ada go d m2 --enable-offload-targets=nvptx-none  --enable-offload-defaulted --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/13 --with-libstdcxx-zoneinfo=/usr/share/zoneinfo --enable-ssp --disable-libssp --disable-libvtv --enable-cet=auto --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-libphobos --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-13 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --enable-link-serialization --build=x86_64-suse-linux --host=x86_64-suse-linux]
  ignore line: [Thread model: posix]
  ignore line: [Supported LTO compression algorithms: zlib]
  ignore line: [gcc version 13.2.1 20240206 [revision 67ac78caf31f7cb3202177e6428a46d829b70f23] (SUSE Linux) ]
  ignore line: [COLLECT_GCC_OPTIONS='-D' '__CUDA_ARCH__=800' '-D' '__CUDA_ARCH_LIST__=800' '-D' '__NV_LEGACY_LAUNCH' '-c' '-D' 'CUDA_DOUBLE_MATH_FUNCTIONS' '-v' '-Wno-psabi' '-I' '/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include' '-m64' '-o' 'CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_8b833.dir/']
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/13/cc1plus -quiet -v -I /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include -D_GNU_SOURCE -D __CUDA_ARCH__=800 -D __CUDA_ARCH_LIST__=800 -D __NV_LEGACY_LAUNCH -D CUDA_DOUBLE_MATH_FUNCTIONS /tmp/tmpxft_0015f755_00000000-6_CMakeCUDACompilerABI.cudafe1.cpp -quiet -dumpdir CMakeFiles/cmTC_8b833.dir/ -dumpbase CMakeCUDACompilerABI.cu.cpp -dumpbase-ext .cpp -m64 -mtune=generic -march=x86-64 -Wno-psabi -version -o /tmp/ccIsNtP0.s]
  ignore line: [GNU C++17 (SUSE Linux) version 13.2.1 20240206 [revision 67ac78caf31f7cb3202177e6428a46d829b70f23] (x86_64-suse-linux)]
  ignore line: [	compiled by GNU C version 13.2.1 20240206 [revision 67ac78caf31f7cb3202177e6428a46d829b70f23]  GMP version 6.1.2  MPFR version 4.0.2-p6  MPC version 1.1.0  isl version isl-0.18-GMP]
  ignore line: []
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [ignoring duplicate directory "/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include]
  ignore line: [ /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include]
  ignore line: [ /usr/include/c++/13]
  ignore line: [ /usr/include/c++/13/x86_64-suse-linux]
  ignore line: [ /usr/include/c++/13/backward]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/13/include]
  ignore line: [ /usr/local/include]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/13/include-fixed]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/include]
  ignore line: [ /usr/include]
  ignore line: [End of search list.]
  ignore line: [Compiler executable checksum: 00000000000000000000000000000000]
  ignore line: [COLLECT_GCC_OPTIONS='-D' '__CUDA_ARCH__=800' '-D' '__CUDA_ARCH_LIST__=800' '-D' '__NV_LEGACY_LAUNCH' '-c' '-D' 'CUDA_DOUBLE_MATH_FUNCTIONS' '-v' '-Wno-psabi' '-I' '/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include' '-m64' '-o' 'CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_8b833.dir/']
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/bin/as -v -I /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include --64 -o CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o /tmp/ccIsNtP0.s]
  ignore line: [GNU assembler version 2.43.1 (x86_64-suse-linux) using BFD version (GNU Binutils]
  ignore line: [ SUSE Linux Enterprise 15) 2.43.1.20241209-150100.7.52]
  ignore line: [COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/bin/]
  ignore line: [LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-D' '__CUDA_ARCH__=800' '-D' '__CUDA_ARCH_LIST__=800' '-D' '__NV_LEGACY_LAUNCH' '-c' '-D' 'CUDA_DOUBLE_MATH_FUNCTIONS' '-v' '-Wno-psabi' '-I' '/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/../targets/x86_64-linux/include' '-m64' '-o' 'CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.']
  ignore line: [Linking CUDA executable cmTC_8b833]
  ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_8b833.dir/link.txt --verbose=1]
  ignore line: [/opt/cray/pe/gcc-native/13/bin/g++  -v CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o -o cmTC_8b833  -lcudadevrt -lcudart_static -lrt -lpthread -ldl  -L"/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs" -L"/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib"]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/opt/cray/pe/gcc-native/13/bin/g++]
  ignore line: [COLLECT_LTO_WRAPPER=/usr/lib64/gcc/x86_64-suse-linux/13/lto-wrapper]
  ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
  ignore line: [OFFLOAD_TARGET_DEFAULT=1]
  ignore line: [Target: x86_64-suse-linux]
  ignore line: [Configured with: ../configure CFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' CXXFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' XCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' TCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' GDCFLAGS=' -fmessage-length=0 -grecord-gcc-switches -O2 -funwind-tables -fasynchronous-unwind-tables -fstack-clash-protection -g' --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c c++ objc fortran obj-c++ ada go d m2 --enable-offload-targets=nvptx-none  --enable-offload-defaulted --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/13 --with-libstdcxx-zoneinfo=/usr/share/zoneinfo --enable-ssp --disable-libssp --disable-libvtv --enable-cet=auto --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-libphobos --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-13 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --enable-link-serialization --build=x86_64-suse-linux --host=x86_64-suse-linux]
  ignore line: [Thread model: posix]
  ignore line: [Supported LTO compression algorithms: zlib]
  ignore line: [gcc version 13.2.1 20240206 [revision 67ac78caf31f7cb3202177e6428a46d829b70f23] (SUSE Linux) ]
  ignore line: [COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/bin/]
  ignore line: [LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/13/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/13/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_8b833' '-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs' '-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_8b833.']
  link line: [ /usr/lib64/gcc/x86_64-suse-linux/13/collect2 -plugin /usr/lib64/gcc/x86_64-suse-linux/13/liblto_plugin.so -plugin-opt=/usr/lib64/gcc/x86_64-suse-linux/13/lto-wrapper -plugin-opt=-fresolution=/tmp/cccw2Gcq.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_8b833 /usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crt1.o /usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crti.o /usr/lib64/gcc/x86_64-suse-linux/13/crtbegin.o -L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs -L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib -L/usr/lib64/gcc/x86_64-suse-linux/13 -L/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/lib -L/usr/lib64/gcc/x86_64-suse-linux/13/../../.. CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o -lcudadevrt -lcudart_static -lrt -lpthread -ldl -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib64/gcc/x86_64-suse-linux/13/crtend.o /usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crtn.o]
    arg [/usr/lib64/gcc/x86_64-suse-linux/13/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/usr/lib64/gcc/x86_64-suse-linux/13/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/usr/lib64/gcc/x86_64-suse-linux/13/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/cccw2Gcq.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [--build-id] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_x86_64] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_8b833] ==> ignore
    arg [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crt1.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crt1.o]
    arg [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crti.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crti.o]
    arg [/usr/lib64/gcc/x86_64-suse-linux/13/crtbegin.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/13/crtbegin.o]
    arg [-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs] ==> dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs]
    arg [-L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib] ==> dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib]
    arg [-L/usr/lib64/gcc/x86_64-suse-linux/13] ==> dir [/usr/lib64/gcc/x86_64-suse-linux/13]
    arg [-L/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64] ==> dir [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64]
    arg [-L/lib/../lib64] ==> dir [/lib/../lib64]
    arg [-L/usr/lib/../lib64] ==> dir [/usr/lib/../lib64]
    arg [-L/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/lib] ==> dir [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/lib]
    arg [-L/usr/lib64/gcc/x86_64-suse-linux/13/../../..] ==> dir [/usr/lib64/gcc/x86_64-suse-linux/13/../../..]
    arg [CMakeFiles/cmTC_8b833.dir/CMakeCUDACompilerABI.cu.o] ==> ignore
    arg [-lcudadevrt] ==> lib [cudadevrt]
    arg [-lcudart_static] ==> lib [cudart_static]
    arg [-lrt] ==> lib [rt]
    arg [-lpthread] ==> lib [pthread]
    arg [-ldl] ==> lib [dl]
    arg [-lstdc++] ==> lib [stdc++]
    arg [-lm] ==> lib [m]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [-lc] ==> lib [c]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [/usr/lib64/gcc/x86_64-suse-linux/13/crtend.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/13/crtend.o]
    arg [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crtn.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crtn.o]
  collapse obj [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crt1.o] ==> [/usr/lib64/crt1.o]
  collapse obj [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crti.o] ==> [/usr/lib64/crti.o]
  collapse obj [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64/crtn.o] ==> [/usr/lib64/crtn.o]
  collapse library dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs] ==> [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs]
  collapse library dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib] ==> [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib]
  collapse library dir [/usr/lib64/gcc/x86_64-suse-linux/13] ==> [/usr/lib64/gcc/x86_64-suse-linux/13]
  collapse library dir [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../lib64] ==> [/usr/lib64]
  collapse library dir [/lib/../lib64] ==> [/lib64]
  collapse library dir [/usr/lib/../lib64] ==> [/usr/lib64]
  collapse library dir [/usr/lib64/gcc/x86_64-suse-linux/13/../../../../x86_64-suse-linux/lib] ==> [/usr/x86_64-suse-linux/lib]
  collapse library dir [/usr/lib64/gcc/x86_64-suse-linux/13/../../..] ==> [/usr/lib64]
  implicit libs: [cudadevrt;cudart_static;rt;pthread;dl;stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
  implicit objs: [/usr/lib64/crt1.o;/usr/lib64/crti.o;/usr/lib64/gcc/x86_64-suse-linux/13/crtbegin.o;/usr/lib64/gcc/x86_64-suse-linux/13/crtend.o;/usr/lib64/crtn.o]
  implicit dirs: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs;/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib;/usr/lib64/gcc/x86_64-suse-linux/13;/usr/lib64;/lib64;/usr/x86_64-suse-linux/lib]
  implicit fwks: []


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_8be51/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_8be51.dir/build.make CMakeFiles/cmTC_8be51.dir/build
gmake[1]: Entering directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o
/usr/bin/c++   -v -o CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp
Using built-in specs.
COLLECT_GCC=/usr/bin/c++
OFFLOAD_TARGET_NAMES=hsa:nvptx-none
Target: x86_64-suse-linux
Configured with: ../configure --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c,c++,objc,fortran,obj-c++,ada,go --enable-offload-targets=hsa,nvptx-none, --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/7 --enable-ssp --disable-libssp --disable-libvtv --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-7 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --build=x86_64-suse-linux --host=x86_64-suse-linux
Thread model: posix
gcc version 7.5.0 (SUSE Linux) 
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
 /usr/lib64/gcc/x86_64-suse-linux/7/cc1plus -quiet -v -D_GNU_SOURCE /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o -version -o /tmp/ccmY783P.s
GNU C++14 (SUSE Linux) version 7.5.0 (x86_64-suse-linux)
	compiled by GNU C version 7.5.0, GMP version 6.1.2, MPFR version 4.0.2-p6, MPC version 1.1.0, isl version isl-0.18-GMP

GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
#include "..." search starts here:
#include <...> search starts here:
 /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include
 /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include
 /usr/include/c++/7
 /usr/include/c++/7/x86_64-suse-linux
 /usr/include/c++/7/backward
 /usr/lib64/gcc/x86_64-suse-linux/7/include
 /usr/local/include
 /usr/lib64/gcc/x86_64-suse-linux/7/include-fixed
 /usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/include
 /usr/include
End of search list.
GNU C++14 (SUSE Linux) version 7.5.0 (x86_64-suse-linux)
	compiled by GNU C version 7.5.0, GMP version 6.1.2, MPFR version 4.0.2-p6, MPC version 1.1.0, isl version isl-0.18-GMP

GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
Compiler executable checksum: b173113e71986c3d169e65fd129b9b5e
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
 /usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/bin/as -v --64 -o CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccmY783P.s
GNU assembler version 2.43.1 (x86_64-suse-linux) using BFD version (GNU Binutils; SUSE Linux Enterprise 15) 2.43.1.20241209-150100.7.52
COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/bin/
LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
Linking CXX executable cmTC_8be51
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_8be51.dir/link.txt --verbose=1
/usr/bin/c++  -v CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_8be51 
Using built-in specs.
COLLECT_GCC=/usr/bin/c++
COLLECT_LTO_WRAPPER=/usr/lib64/gcc/x86_64-suse-linux/7/lto-wrapper
OFFLOAD_TARGET_NAMES=hsa:nvptx-none
Target: x86_64-suse-linux
Configured with: ../configure --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c,c++,objc,fortran,obj-c++,ada,go --enable-offload-targets=hsa,nvptx-none, --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/7 --enable-ssp --disable-libssp --disable-libvtv --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-7 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --build=x86_64-suse-linux --host=x86_64-suse-linux
Thread model: posix
gcc version 7.5.0 (SUSE Linux) 
COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/bin/
LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_8be51' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
 /usr/lib64/gcc/x86_64-suse-linux/7/collect2 -plugin /usr/lib64/gcc/x86_64-suse-linux/7/liblto_plugin.so -plugin-opt=/usr/lib64/gcc/x86_64-suse-linux/7/lto-wrapper -plugin-opt=-fresolution=/tmp/ccUghI9b.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_8be51 /usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crt1.o /usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crti.o /usr/lib64/gcc/x86_64-suse-linux/7/crtbegin.o -L/usr/lib64/gcc/x86_64-suse-linux/7 -L/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/lib -L/usr/lib64/gcc/x86_64-suse-linux/7/../../.. CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib64/gcc/x86_64-suse-linux/7/crtend.o /usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crtn.o
COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_8be51' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
gmake[1]: Leaving directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'



Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include]
    add: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include]
    add: [/usr/include/c++/7]
    add: [/usr/include/c++/7/x86_64-suse-linux]
    add: [/usr/include/c++/7/backward]
    add: [/usr/lib64/gcc/x86_64-suse-linux/7/include]
    add: [/usr/local/include]
    add: [/usr/lib64/gcc/x86_64-suse-linux/7/include-fixed]
    add: [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/include]
    add: [/usr/include]
  end of search list found
  collapse include dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include] ==> [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include]
  collapse include dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include] ==> [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include]
  collapse include dir [/usr/include/c++/7] ==> [/usr/include/c++/7]
  collapse include dir [/usr/include/c++/7/x86_64-suse-linux] ==> [/usr/include/c++/7/x86_64-suse-linux]
  collapse include dir [/usr/include/c++/7/backward] ==> [/usr/include/c++/7/backward]
  collapse include dir [/usr/lib64/gcc/x86_64-suse-linux/7/include] ==> [/usr/lib64/gcc/x86_64-suse-linux/7/include]
  collapse include dir [/usr/local/include] ==> [/usr/local/include]
  collapse include dir [/usr/lib64/gcc/x86_64-suse-linux/7/include-fixed] ==> [/usr/lib64/gcc/x86_64-suse-linux/7/include-fixed]
  collapse include dir [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/include] ==> [/usr/x86_64-suse-linux/include]
  collapse include dir [/usr/include] ==> [/usr/include]
  implicit include dirs: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include;/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include;/usr/include/c++/7;/usr/include/c++/7/x86_64-suse-linux;/usr/include/c++/7/backward;/usr/lib64/gcc/x86_64-suse-linux/7/include;/usr/local/include;/usr/lib64/gcc/x86_64-suse-linux/7/include-fixed;/usr/x86_64-suse-linux/include;/usr/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_8be51/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_8be51.dir/build.make CMakeFiles/cmTC_8be51.dir/build]
  ignore line: [gmake[1]: Entering directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp']
  ignore line: [Building CXX object CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [/usr/bin/c++   -v -o CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/usr/bin/c++]
  ignore line: [OFFLOAD_TARGET_NAMES=hsa:nvptx-none]
  ignore line: [Target: x86_64-suse-linux]
  ignore line: [Configured with: ../configure --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c c++ objc fortran obj-c++ ada go --enable-offload-targets=hsa nvptx-none  --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/7 --enable-ssp --disable-libssp --disable-libvtv --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-7 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --build=x86_64-suse-linux --host=x86_64-suse-linux]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 7.5.0 (SUSE Linux) ]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/7/cc1plus -quiet -v -D_GNU_SOURCE /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o -version -o /tmp/ccmY783P.s]
  ignore line: [GNU C++14 (SUSE Linux) version 7.5.0 (x86_64-suse-linux)]
  ignore line: [	compiled by GNU C version 7.5.0  GMP version 6.1.2  MPFR version 4.0.2-p6  MPC version 1.1.0  isl version isl-0.18-GMP]
  ignore line: []
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include]
  ignore line: [ /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include]
  ignore line: [ /usr/include/c++/7]
  ignore line: [ /usr/include/c++/7/x86_64-suse-linux]
  ignore line: [ /usr/include/c++/7/backward]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/7/include]
  ignore line: [ /usr/local/include]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/7/include-fixed]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/include]
  ignore line: [ /usr/include]
  ignore line: [End of search list.]
  ignore line: [GNU C++14 (SUSE Linux) version 7.5.0 (x86_64-suse-linux)]
  ignore line: [	compiled by GNU C version 7.5.0  GMP version 6.1.2  MPFR version 4.0.2-p6  MPC version 1.1.0  isl version isl-0.18-GMP]
  ignore line: []
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [Compiler executable checksum: b173113e71986c3d169e65fd129b9b5e]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/bin/as -v --64 -o CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccmY783P.s]
  ignore line: [GNU assembler version 2.43.1 (x86_64-suse-linux) using BFD version (GNU Binutils]
  ignore line: [ SUSE Linux Enterprise 15) 2.43.1.20241209-150100.7.52]
  ignore line: [COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/bin/]
  ignore line: [LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
  ignore line: [Linking CXX executable cmTC_8be51]
  ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_8be51.dir/link.txt --verbose=1]
  ignore line: [/usr/bin/c++  -v CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_8be51 ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/usr/bin/c++]
  ignore line: [COLLECT_LTO_WRAPPER=/usr/lib64/gcc/x86_64-suse-linux/7/lto-wrapper]
  ignore line: [OFFLOAD_TARGET_NAMES=hsa:nvptx-none]
  ignore line: [Target: x86_64-suse-linux]
  ignore line: [Configured with: ../configure --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c c++ objc fortran obj-c++ ada go --enable-offload-targets=hsa nvptx-none  --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/7 --enable-ssp --disable-libssp --disable-libvtv --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-7 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --build=x86_64-suse-linux --host=x86_64-suse-linux]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 7.5.0 (SUSE Linux) ]
  ignore line: [COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/bin/]
  ignore line: [LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/7/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/7/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_8be51' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
  link line: [ /usr/lib64/gcc/x86_64-suse-linux/7/collect2 -plugin /usr/lib64/gcc/x86_64-suse-linux/7/liblto_plugin.so -plugin-opt=/usr/lib64/gcc/x86_64-suse-linux/7/lto-wrapper -plugin-opt=-fresolution=/tmp/ccUghI9b.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_8be51 /usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crt1.o /usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crti.o /usr/lib64/gcc/x86_64-suse-linux/7/crtbegin.o -L/usr/lib64/gcc/x86_64-suse-linux/7 -L/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/lib -L/usr/lib64/gcc/x86_64-suse-linux/7/../../.. CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib64/gcc/x86_64-suse-linux/7/crtend.o /usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crtn.o]
    arg [/usr/lib64/gcc/x86_64-suse-linux/7/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/usr/lib64/gcc/x86_64-suse-linux/7/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/usr/lib64/gcc/x86_64-suse-linux/7/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/ccUghI9b.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [--build-id] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_x86_64] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_8be51] ==> ignore
    arg [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crt1.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crt1.o]
    arg [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crti.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crti.o]
    arg [/usr/lib64/gcc/x86_64-suse-linux/7/crtbegin.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/7/crtbegin.o]
    arg [-L/usr/lib64/gcc/x86_64-suse-linux/7] ==> dir [/usr/lib64/gcc/x86_64-suse-linux/7]
    arg [-L/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64] ==> dir [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64]
    arg [-L/lib/../lib64] ==> dir [/lib/../lib64]
    arg [-L/usr/lib/../lib64] ==> dir [/usr/lib/../lib64]
    arg [-L/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/lib] ==> dir [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/lib]
    arg [-L/usr/lib64/gcc/x86_64-suse-linux/7/../../..] ==> dir [/usr/lib64/gcc/x86_64-suse-linux/7/../../..]
    arg [CMakeFiles/cmTC_8be51.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-lstdc++] ==> lib [stdc++]
    arg [-lm] ==> lib [m]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [-lc] ==> lib [c]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [/usr/lib64/gcc/x86_64-suse-linux/7/crtend.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/7/crtend.o]
    arg [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crtn.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crtn.o]
  collapse obj [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crt1.o] ==> [/usr/lib64/crt1.o]
  collapse obj [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crti.o] ==> [/usr/lib64/crti.o]
  collapse obj [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64/crtn.o] ==> [/usr/lib64/crtn.o]
  collapse library dir [/usr/lib64/gcc/x86_64-suse-linux/7] ==> [/usr/lib64/gcc/x86_64-suse-linux/7]
  collapse library dir [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../lib64] ==> [/usr/lib64]
  collapse library dir [/lib/../lib64] ==> [/lib64]
  collapse library dir [/usr/lib/../lib64] ==> [/usr/lib64]
  collapse library dir [/usr/lib64/gcc/x86_64-suse-linux/7/../../../../x86_64-suse-linux/lib] ==> [/usr/x86_64-suse-linux/lib]
  collapse library dir [/usr/lib64/gcc/x86_64-suse-linux/7/../../..] ==> [/usr/lib64]
  implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
  implicit objs: [/usr/lib64/crt1.o;/usr/lib64/crti.o;/usr/lib64/gcc/x86_64-suse-linux/7/crtbegin.o;/usr/lib64/gcc/x86_64-suse-linux/7/crtend.o;/usr/lib64/crtn.o]
  implicit dirs: [/usr/lib64/gcc/x86_64-suse-linux/7;/usr/lib64;/lib64;/usr/x86_64-suse-linux/lib]
  implicit fwks: []


Detecting C compiler ABI info compiled with the following output:
Change Dir: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_7d5f2/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_7d5f2.dir/build.make CMakeFiles/cmTC_7d5f2.dir/build
gmake[1]: Entering directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o
/usr/bin/cc   -v -o CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake/Modules/CMakeCCompilerABI.c
Using built-in specs.
COLLECT_GCC=/usr/bin/cc
OFFLOAD_TARGET_NAMES=nvptx-none
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-suse-linux
Configured with: ../configure --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c,c++,objc,fortran,obj-c++,ada,go,d --enable-offload-targets=nvptx-none, --enable-offload-defaulted --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/12 --enable-ssp --disable-libssp --disable-libvtv --enable-cet=auto --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-libphobos --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-12 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --enable-link-serialization --build=x86_64-suse-linux --host=x86_64-suse-linux
Thread model: posix
Supported LTO compression algorithms: zlib
gcc version 12.3.0 (SUSE Linux) 
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_7d5f2.dir/'
 /usr/lib64/gcc/x86_64-suse-linux/12/cc1 -quiet -v /usr/share/cmake/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_7d5f2.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=x86-64 -version -o /tmp/cc7POJgR.s
GNU C17 (SUSE Linux) version 12.3.0 (x86_64-suse-linux)
	compiled by GNU C version 12.3.0, GMP version 6.1.2, MPFR version 4.0.2-p6, MPC version 1.1.0, isl version isl-0.18-GMP

GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
#include "..." search starts here:
#include <...> search starts here:
 /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include
 /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include
 /usr/lib64/gcc/x86_64-suse-linux/12/include
 /usr/local/include
 /usr/lib64/gcc/x86_64-suse-linux/12/include-fixed
 /usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/include
 /usr/include
End of search list.
GNU C17 (SUSE Linux) version 12.3.0 (x86_64-suse-linux)
	compiled by GNU C version 12.3.0, GMP version 6.1.2, MPFR version 4.0.2-p6, MPC version 1.1.0, isl version isl-0.18-GMP

GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
Compiler executable checksum: 00000000000000000000000000000000
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_7d5f2.dir/'
 /usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/bin/as -v --64 -o CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o /tmp/cc7POJgR.s
GNU assembler version 2.43.1 (x86_64-suse-linux) using BFD version (GNU Binutils; SUSE Linux Enterprise 15) 2.43.1.20241209-150100.7.52
COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/bin/
LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.'
Linking C executable cmTC_7d5f2
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_7d5f2.dir/link.txt --verbose=1
/usr/bin/cc  -v CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o -o cmTC_7d5f2 
Using built-in specs.
COLLECT_GCC=/usr/bin/cc
COLLECT_LTO_WRAPPER=/usr/lib64/gcc/x86_64-suse-linux/12/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-suse-linux
Configured with: ../configure --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c,c++,objc,fortran,obj-c++,ada,go,d --enable-offload-targets=nvptx-none, --enable-offload-defaulted --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/12 --enable-ssp --disable-libssp --disable-libvtv --enable-cet=auto --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-libphobos --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-12 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --enable-link-serialization --build=x86_64-suse-linux --host=x86_64-suse-linux
Thread model: posix
Supported LTO compression algorithms: zlib
gcc version 12.3.0 (SUSE Linux) 
COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/bin/
LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_7d5f2' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_7d5f2.'
 /usr/lib64/gcc/x86_64-suse-linux/12/collect2 -plugin /usr/lib64/gcc/x86_64-suse-linux/12/liblto_plugin.so -plugin-opt=/usr/lib64/gcc/x86_64-suse-linux/12/lto-wrapper -plugin-opt=-fresolution=/tmp/cc14bSTe.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_7d5f2 /usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crt1.o /usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crti.o /usr/lib64/gcc/x86_64-suse-linux/12/crtbegin.o -L/usr/lib64/gcc/x86_64-suse-linux/12 -L/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/lib -L/usr/lib64/gcc/x86_64-suse-linux/12/../../.. CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib64/gcc/x86_64-suse-linux/12/crtend.o /usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crtn.o
COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_7d5f2' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_7d5f2.'
gmake[1]: Leaving directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'



Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include]
    add: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include]
    add: [/usr/lib64/gcc/x86_64-suse-linux/12/include]
    add: [/usr/local/include]
    add: [/usr/lib64/gcc/x86_64-suse-linux/12/include-fixed]
    add: [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/include]
    add: [/usr/include]
  end of search list found
  collapse include dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include] ==> [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include]
  collapse include dir [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include] ==> [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include]
  collapse include dir [/usr/lib64/gcc/x86_64-suse-linux/12/include] ==> [/usr/lib64/gcc/x86_64-suse-linux/12/include]
  collapse include dir [/usr/local/include] ==> [/usr/local/include]
  collapse include dir [/usr/lib64/gcc/x86_64-suse-linux/12/include-fixed] ==> [/usr/lib64/gcc/x86_64-suse-linux/12/include-fixed]
  collapse include dir [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/include] ==> [/usr/x86_64-suse-linux/include]
  collapse include dir [/usr/include] ==> [/usr/include]
  implicit include dirs: [/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include;/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include;/usr/lib64/gcc/x86_64-suse-linux/12/include;/usr/local/include;/usr/lib64/gcc/x86_64-suse-linux/12/include-fixed;/usr/x86_64-suse-linux/include;/usr/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_7d5f2/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_7d5f2.dir/build.make CMakeFiles/cmTC_7d5f2.dir/build]
  ignore line: [gmake[1]: Entering directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp']
  ignore line: [Building C object CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o]
  ignore line: [/usr/bin/cc   -v -o CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake/Modules/CMakeCCompilerABI.c]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/usr/bin/cc]
  ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
  ignore line: [OFFLOAD_TARGET_DEFAULT=1]
  ignore line: [Target: x86_64-suse-linux]
  ignore line: [Configured with: ../configure --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c c++ objc fortran obj-c++ ada go d --enable-offload-targets=nvptx-none  --enable-offload-defaulted --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/12 --enable-ssp --disable-libssp --disable-libvtv --enable-cet=auto --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-libphobos --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-12 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --enable-link-serialization --build=x86_64-suse-linux --host=x86_64-suse-linux]
  ignore line: [Thread model: posix]
  ignore line: [Supported LTO compression algorithms: zlib]
  ignore line: [gcc version 12.3.0 (SUSE Linux) ]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_7d5f2.dir/']
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/12/cc1 -quiet -v /usr/share/cmake/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_7d5f2.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=x86-64 -version -o /tmp/cc7POJgR.s]
  ignore line: [GNU C17 (SUSE Linux) version 12.3.0 (x86_64-suse-linux)]
  ignore line: [	compiled by GNU C version 12.3.0  GMP version 6.1.2  MPFR version 4.0.2-p6  MPC version 1.1.0  isl version isl-0.18-GMP]
  ignore line: []
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/math_libs/12.4/include]
  ignore line: [ /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/12/include]
  ignore line: [ /usr/local/include]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/12/include-fixed]
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/include]
  ignore line: [ /usr/include]
  ignore line: [End of search list.]
  ignore line: [GNU C17 (SUSE Linux) version 12.3.0 (x86_64-suse-linux)]
  ignore line: [	compiled by GNU C version 12.3.0  GMP version 6.1.2  MPFR version 4.0.2-p6  MPC version 1.1.0  isl version isl-0.18-GMP]
  ignore line: []
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [Compiler executable checksum: 00000000000000000000000000000000]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_7d5f2.dir/']
  ignore line: [ /usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/bin/as -v --64 -o CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o /tmp/cc7POJgR.s]
  ignore line: [GNU assembler version 2.43.1 (x86_64-suse-linux) using BFD version (GNU Binutils]
  ignore line: [ SUSE Linux Enterprise 15) 2.43.1.20241209-150100.7.52]
  ignore line: [COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/bin/]
  ignore line: [LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.']
  ignore line: [Linking C executable cmTC_7d5f2]
  ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_7d5f2.dir/link.txt --verbose=1]
  ignore line: [/usr/bin/cc  -v CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o -o cmTC_7d5f2 ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/usr/bin/cc]
  ignore line: [COLLECT_LTO_WRAPPER=/usr/lib64/gcc/x86_64-suse-linux/12/lto-wrapper]
  ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
  ignore line: [OFFLOAD_TARGET_DEFAULT=1]
  ignore line: [Target: x86_64-suse-linux]
  ignore line: [Configured with: ../configure --prefix=/usr --infodir=/usr/share/info --mandir=/usr/share/man --libdir=/usr/lib64 --libexecdir=/usr/lib64 --enable-languages=c c++ objc fortran obj-c++ ada go d --enable-offload-targets=nvptx-none  --enable-offload-defaulted --without-cuda-driver --enable-checking=release --disable-werror --with-gxx-include-dir=/usr/include/c++/12 --enable-ssp --disable-libssp --disable-libvtv --enable-cet=auto --disable-libcc1 --disable-plugin --with-bugurl=https://bugs.opensuse.org/ --with-pkgversion='SUSE Linux' --with-slibdir=/lib64 --with-system-zlib --enable-libstdcxx-allocator=new --disable-libstdcxx-pch --enable-libphobos --enable-version-specific-runtime-libs --with-gcc-major-version-only --enable-linker-build-id --enable-linux-futex --enable-gnu-indirect-function --program-suffix=-12 --without-system-libunwind --enable-multilib --with-arch-32=x86-64 --with-tune=generic --enable-link-serialization --build=x86_64-suse-linux --host=x86_64-suse-linux]
  ignore line: [Thread model: posix]
  ignore line: [Supported LTO compression algorithms: zlib]
  ignore line: [gcc version 12.3.0 (SUSE Linux) ]
  ignore line: [COMPILER_PATH=/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/bin/]
  ignore line: [LIBRARY_PATH=/usr/lib64/gcc/x86_64-suse-linux/12/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/lib/:/usr/lib64/gcc/x86_64-suse-linux/12/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_7d5f2' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_7d5f2.']
  link line: [ /usr/lib64/gcc/x86_64-suse-linux/12/collect2 -plugin /usr/lib64/gcc/x86_64-suse-linux/12/liblto_plugin.so -plugin-opt=/usr/lib64/gcc/x86_64-suse-linux/12/lto-wrapper -plugin-opt=-fresolution=/tmp/cc14bSTe.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_7d5f2 /usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crt1.o /usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crti.o /usr/lib64/gcc/x86_64-suse-linux/12/crtbegin.o -L/usr/lib64/gcc/x86_64-suse-linux/12 -L/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/lib -L/usr/lib64/gcc/x86_64-suse-linux/12/../../.. CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib64/gcc/x86_64-suse-linux/12/crtend.o /usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crtn.o]
    arg [/usr/lib64/gcc/x86_64-suse-linux/12/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/usr/lib64/gcc/x86_64-suse-linux/12/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/usr/lib64/gcc/x86_64-suse-linux/12/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/cc14bSTe.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [--build-id] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_x86_64] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_7d5f2] ==> ignore
    arg [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crt1.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crt1.o]
    arg [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crti.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crti.o]
    arg [/usr/lib64/gcc/x86_64-suse-linux/12/crtbegin.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/12/crtbegin.o]
    arg [-L/usr/lib64/gcc/x86_64-suse-linux/12] ==> dir [/usr/lib64/gcc/x86_64-suse-linux/12]
    arg [-L/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64] ==> dir [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64]
    arg [-L/lib/../lib64] ==> dir [/lib/../lib64]
    arg [-L/usr/lib/../lib64] ==> dir [/usr/lib/../lib64]
    arg [-L/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/lib] ==> dir [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/lib]
    arg [-L/usr/lib64/gcc/x86_64-suse-linux/12/../../..] ==> dir [/usr/lib64/gcc/x86_64-suse-linux/12/../../..]
    arg [CMakeFiles/cmTC_7d5f2.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [-lgcc] ==> lib [gcc]
    arg [--push-state] ==> ignore
    arg [--as-needed] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [--pop-state] ==> ignore
    arg [-lc] ==> lib [c]
    arg [-lgcc] ==> lib [gcc]
    arg [--push-state] ==> ignore
    arg [--as-needed] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [--pop-state] ==> ignore
    arg [/usr/lib64/gcc/x86_64-suse-linux/12/crtend.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/12/crtend.o]
    arg [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crtn.o] ==> obj [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crtn.o]
  collapse obj [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crt1.o] ==> [/usr/lib64/crt1.o]
  collapse obj [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crti.o] ==> [/usr/lib64/crti.o]
  collapse obj [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64/crtn.o] ==> [/usr/lib64/crtn.o]
  collapse library dir [/usr/lib64/gcc/x86_64-suse-linux/12] ==> [/usr/lib64/gcc/x86_64-suse-linux/12]
  collapse library dir [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../lib64] ==> [/usr/lib64]
  collapse library dir [/lib/../lib64] ==> [/lib64]
  collapse library dir [/usr/lib/../lib64] ==> [/usr/lib64]
  collapse library dir [/usr/lib64/gcc/x86_64-suse-linux/12/../../../../x86_64-suse-linux/lib] ==> [/usr/x86_64-suse-linux/lib]
  collapse library dir [/usr/lib64/gcc/x86_64-suse-linux/12/../../..] ==> [/usr/lib64]
  implicit libs: [gcc;gcc_s;c;gcc;gcc_s]
  implicit objs: [/usr/lib64/crt1.o;/usr/lib64/crti.o;/usr/lib64/gcc/x86_64-suse-linux/12/crtbegin.o;/usr/lib64/gcc/x86_64-suse-linux/12/crtend.o;/usr/lib64/crtn.o]
  implicit dirs: [/usr/lib64/gcc/x86_64-suse-linux/12;/usr/lib64;/lib64;/usr/x86_64-suse-linux/lib]
  implicit fwks: []


Determining if the include file pthread.h exists passed with the following output:
Change Dir: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_53871/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_53871.dir/build.make CMakeFiles/cmTC_53871.dir/build
gmake[1]: Entering directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_53871.dir/CheckIncludeFile.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_53871.dir/CheckIncludeFile.c.o -c /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp/CheckIncludeFile.c
Linking C executable cmTC_53871
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_53871.dir/link.txt --verbose=1
/usr/bin/cc CMakeFiles/cmTC_53871.dir/CheckIncludeFile.c.o -o cmTC_53871 
gmake[1]: Leaving directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'



Determining if the function pthread_create exists in the pthread passed with the following output:
Change Dir: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_e6ae1/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_e6ae1.dir/build.make CMakeFiles/cmTC_e6ae1.dir/build
gmake[1]: Entering directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_e6ae1.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create -o CMakeFiles/cmTC_e6ae1.dir/CheckFunctionExists.c.o -c /usr/share/cmake/Modules/CheckFunctionExists.c
Linking C executable cmTC_e6ae1
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_e6ae1.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create CMakeFiles/cmTC_e6ae1.dir/CheckFunctionExists.c.o -o cmTC_e6ae1  -lpthread 
gmake[1]: Leaving directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'



Performing CUDA SOURCE FILE Test NVCC_THREADS succeeded with the following output:
Change Dir: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_dd405/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_dd405.dir/build.make CMakeFiles/cmTC_dd405.dir/build
gmake[1]: Entering directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'
Building CUDA object CMakeFiles/cmTC_dd405.dir/src.cu.o
/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/nvcc -forward-unknown-to-host-compiler -DNVCC_THREADS  --generate-code=arch=compute_80,code=[compute_80,sm_80]   -t4 -MD -MT CMakeFiles/cmTC_dd405.dir/src.cu.o -MF CMakeFiles/cmTC_dd405.dir/src.cu.o.d -x cu -c /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp/src.cu -o CMakeFiles/cmTC_dd405.dir/src.cu.o
Linking CUDA executable cmTC_dd405
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_dd405.dir/link.txt --verbose=1
/opt/cray/pe/gcc-native/13/bin/g++ -Wl,--enable-new-dtags  CMakeFiles/cmTC_dd405.dir/src.cu.o -o cmTC_dd405  -lcudadevrt -lcudart_static -lrt -lpthread -ldl  -L"/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs" -L"/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib"
gmake[1]: Leaving directory '/global/u1/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles/CMakeTmp'


Source file was:
__host__ int main() { return 0; }
