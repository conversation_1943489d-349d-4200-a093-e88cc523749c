# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: src/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: src/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: src/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory examples

# Recursive "all" directory target.
examples/all: examples/CMakeFiles/collective-launch.dir/all
examples/all: examples/CMakeFiles/dev-guide-ring.dir/all
examples/all: examples/CMakeFiles/on-stream.dir/all
examples/all: examples/CMakeFiles/thread-group.dir/all
examples/all: examples/CMakeFiles/put-block.dir/all
examples/all: examples/CMakeFiles/ring-bcast.dir/all
examples/all: examples/CMakeFiles/ring-reduce.dir/all
examples/all: examples/CMakeFiles/moe_shuffle.dir/all
examples/all: examples/CMakeFiles/user-buffer.dir/all
.PHONY : examples/all

# Recursive "preinstall" directory target.
examples/preinstall:
.PHONY : examples/preinstall

# Recursive "clean" directory target.
examples/clean: examples/CMakeFiles/collective-launch.dir/clean
examples/clean: examples/CMakeFiles/dev-guide-ring.dir/clean
examples/clean: examples/CMakeFiles/on-stream.dir/clean
examples/clean: examples/CMakeFiles/thread-group.dir/clean
examples/clean: examples/CMakeFiles/put-block.dir/clean
examples/clean: examples/CMakeFiles/ring-bcast.dir/clean
examples/clean: examples/CMakeFiles/ring-reduce.dir/clean
examples/clean: examples/CMakeFiles/moe_shuffle.dir/clean
examples/clean: examples/CMakeFiles/user-buffer.dir/clean
.PHONY : examples/clean

#=============================================================================
# Directory level rules for directory perftest

# Recursive "all" directory target.
perftest/all: perftest/common/all
perftest/all: perftest/device/all
perftest/all: perftest/host/all
.PHONY : perftest/all

# Recursive "preinstall" directory target.
perftest/preinstall: perftest/common/preinstall
perftest/preinstall: perftest/device/preinstall
perftest/preinstall: perftest/host/preinstall
.PHONY : perftest/preinstall

# Recursive "clean" directory target.
perftest/clean: perftest/common/clean
perftest/clean: perftest/device/clean
perftest/clean: perftest/host/clean
.PHONY : perftest/clean

#=============================================================================
# Directory level rules for directory perftest/common

# Recursive "all" directory target.
perftest/common/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
.PHONY : perftest/common/all

# Recursive "preinstall" directory target.
perftest/common/preinstall:
.PHONY : perftest/common/preinstall

# Recursive "clean" directory target.
perftest/common/clean: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/clean
.PHONY : perftest/common/clean

#=============================================================================
# Directory level rules for directory perftest/device

# Recursive "all" directory target.
perftest/device/all: perftest/device/tile/all
perftest/device/all: perftest/device/coll/all
perftest/device/all: perftest/device/pt-to-pt/all
.PHONY : perftest/device/all

# Recursive "preinstall" directory target.
perftest/device/preinstall: perftest/device/tile/preinstall
perftest/device/preinstall: perftest/device/coll/preinstall
perftest/device/preinstall: perftest/device/pt-to-pt/preinstall
.PHONY : perftest/device/preinstall

# Recursive "clean" directory target.
perftest/device/clean: perftest/device/tile/clean
perftest/device/clean: perftest/device/coll/clean
perftest/device/clean: perftest/device/pt-to-pt/clean
.PHONY : perftest/device/clean

#=============================================================================
# Directory level rules for directory perftest/device/coll

# Recursive "all" directory target.
perftest/device/coll/all: perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/all
perftest/device/coll/all: perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/all
perftest/device/coll/all: perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/all
perftest/device/coll/all: perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/all
perftest/device/coll/all: perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/all
perftest/device/coll/all: perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/all
perftest/device/coll/all: perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/all
.PHONY : perftest/device/coll/all

# Recursive "preinstall" directory target.
perftest/device/coll/preinstall:
.PHONY : perftest/device/coll/preinstall

# Recursive "clean" directory target.
perftest/device/coll/clean: perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/clean
perftest/device/coll/clean: perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/clean
perftest/device/coll/clean: perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/clean
perftest/device/coll/clean: perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/clean
perftest/device/coll/clean: perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/clean
perftest/device/coll/clean: perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/clean
perftest/device/coll/clean: perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/clean
.PHONY : perftest/device/coll/clean

#=============================================================================
# Directory level rules for directory perftest/device/pt-to-pt

# Recursive "all" directory target.
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/all
perftest/device/pt-to-pt/all: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/all
.PHONY : perftest/device/pt-to-pt/all

# Recursive "preinstall" directory target.
perftest/device/pt-to-pt/preinstall:
.PHONY : perftest/device/pt-to-pt/preinstall

# Recursive "clean" directory target.
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/clean
perftest/device/pt-to-pt/clean: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/clean
.PHONY : perftest/device/pt-to-pt/clean

#=============================================================================
# Directory level rules for directory perftest/device/tile

# Recursive "all" directory target.
perftest/device/tile/all: perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/all
perftest/device/tile/all: perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/all
.PHONY : perftest/device/tile/all

# Recursive "preinstall" directory target.
perftest/device/tile/preinstall:
.PHONY : perftest/device/tile/preinstall

# Recursive "clean" directory target.
perftest/device/tile/clean: perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/clean
perftest/device/tile/clean: perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/clean
.PHONY : perftest/device/tile/clean

#=============================================================================
# Directory level rules for directory perftest/host

# Recursive "all" directory target.
perftest/host/all: perftest/host/coll/all
perftest/host/all: perftest/host/init/all
perftest/host/all: perftest/host/pt-to-pt/all
.PHONY : perftest/host/all

# Recursive "preinstall" directory target.
perftest/host/preinstall: perftest/host/coll/preinstall
perftest/host/preinstall: perftest/host/init/preinstall
perftest/host/preinstall: perftest/host/pt-to-pt/preinstall
.PHONY : perftest/host/preinstall

# Recursive "clean" directory target.
perftest/host/clean: perftest/host/coll/clean
perftest/host/clean: perftest/host/init/clean
perftest/host/clean: perftest/host/pt-to-pt/clean
.PHONY : perftest/host/clean

#=============================================================================
# Directory level rules for directory perftest/host/coll

# Recursive "all" directory target.
perftest/host/coll/all: perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/all
perftest/host/coll/all: perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/all
perftest/host/coll/all: perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/all
perftest/host/coll/all: perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/all
perftest/host/coll/all: perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/all
perftest/host/coll/all: perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/all
perftest/host/coll/all: perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/all
perftest/host/coll/all: perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/all
perftest/host/coll/all: perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/all
.PHONY : perftest/host/coll/all

# Recursive "preinstall" directory target.
perftest/host/coll/preinstall:
.PHONY : perftest/host/coll/preinstall

# Recursive "clean" directory target.
perftest/host/coll/clean: perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/clean
perftest/host/coll/clean: perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/clean
perftest/host/coll/clean: perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/clean
perftest/host/coll/clean: perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/clean
perftest/host/coll/clean: perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/clean
perftest/host/coll/clean: perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/clean
perftest/host/coll/clean: perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/clean
perftest/host/coll/clean: perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/clean
perftest/host/coll/clean: perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/clean
.PHONY : perftest/host/coll/clean

#=============================================================================
# Directory level rules for directory perftest/host/init

# Recursive "all" directory target.
perftest/host/init/all: perftest/host/init/CMakeFiles/perf_host_malloc.dir/all
.PHONY : perftest/host/init/all

# Recursive "preinstall" directory target.
perftest/host/init/preinstall:
.PHONY : perftest/host/init/preinstall

# Recursive "clean" directory target.
perftest/host/init/clean: perftest/host/init/CMakeFiles/perf_host_malloc.dir/clean
.PHONY : perftest/host/init/clean

#=============================================================================
# Directory level rules for directory perftest/host/pt-to-pt

# Recursive "all" directory target.
perftest/host/pt-to-pt/all: perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/all
perftest/host/pt-to-pt/all: perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/all
perftest/host/pt-to-pt/all: perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/all
.PHONY : perftest/host/pt-to-pt/all

# Recursive "preinstall" directory target.
perftest/host/pt-to-pt/preinstall:
.PHONY : perftest/host/pt-to-pt/preinstall

# Recursive "clean" directory target.
perftest/host/pt-to-pt/clean: perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/clean
perftest/host/pt-to-pt/clean: perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/clean
perftest/host/pt-to-pt/clean: perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/clean
.PHONY : perftest/host/pt-to-pt/clean

#=============================================================================
# Directory level rules for directory src

# Recursive "all" directory target.
src/all: src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/all
src/all: src/CMakeFiles/git_commit.dir/all
src/all: src/CMakeFiles/nvshmem_device_project.dir/all
src/all: src/CMakeFiles/nvshmem_bootstrap_pmi.dir/all
src/all: src/CMakeFiles/nvshmem_bootstrap_uid.dir/all
src/all: src/CMakeFiles/nvshmem.dir/all
src/all: src/CMakeFiles/nvshmem-info.dir/all
src/all: src/CMakeFiles/nvshmem_host.dir/all
src/all: src/CMakeFiles/nvshmem_transport_libfabric.dir/all
src/all: examples/all
src/all: perftest/all
.PHONY : src/all

# Recursive "preinstall" directory target.
src/preinstall: examples/preinstall
src/preinstall: perftest/preinstall
.PHONY : src/preinstall

# Recursive "clean" directory target.
src/clean: src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/clean
src/clean: src/CMakeFiles/nvshmem_device_project-build.dir/clean
src/clean: src/CMakeFiles/git_commit.dir/clean
src/clean: src/CMakeFiles/nvshmem_device_project-install.dir/clean
src/clean: src/CMakeFiles/nvshmem_device_project.dir/clean
src/clean: src/CMakeFiles/nvshmem_bootstrap_pmi.dir/clean
src/clean: src/CMakeFiles/nvshmem_bootstrap_uid.dir/clean
src/clean: src/CMakeFiles/nvshmem.dir/clean
src/clean: src/CMakeFiles/nvshmem-info.dir/clean
src/clean: src/CMakeFiles/package_src_target.dir/clean
src/clean: src/CMakeFiles/nvshmem_host.dir/clean
src/clean: src/CMakeFiles/nvshmem_transport_libfabric.dir/clean
src/clean: examples/clean
src/clean: perftest/clean
.PHONY : src/clean

#=============================================================================
# Target rules for target src/CMakeFiles/nvshmem_bootstrap_pmi2.dir

# All Build rule for target.
src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/all:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/build.make src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/build.make src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=28,29 "Built target nvshmem_bootstrap_pmi2"
.PHONY : src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/rule

# Convenience name for target.
nvshmem_bootstrap_pmi2: src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/rule
.PHONY : nvshmem_bootstrap_pmi2

# clean rule for target.
src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/build.make src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/clean
.PHONY : src/CMakeFiles/nvshmem_bootstrap_pmi2.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/nvshmem_device_project-build.dir

# All Build rule for target.
src/CMakeFiles/nvshmem_device_project-build.dir/all:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_device_project-build.dir/build.make src/CMakeFiles/nvshmem_device_project-build.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_device_project-build.dir/build.make src/CMakeFiles/nvshmem_device_project-build.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=31,32 "Built target nvshmem_device_project-build"
.PHONY : src/CMakeFiles/nvshmem_device_project-build.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/nvshmem_device_project-build.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/nvshmem_device_project-build.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : src/CMakeFiles/nvshmem_device_project-build.dir/rule

# Convenience name for target.
nvshmem_device_project-build: src/CMakeFiles/nvshmem_device_project-build.dir/rule
.PHONY : nvshmem_device_project-build

# clean rule for target.
src/CMakeFiles/nvshmem_device_project-build.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_device_project-build.dir/build.make src/CMakeFiles/nvshmem_device_project-build.dir/clean
.PHONY : src/CMakeFiles/nvshmem_device_project-build.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/git_commit.dir

# All Build rule for target.
src/CMakeFiles/git_commit.dir/all:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/git_commit.dir/build.make src/CMakeFiles/git_commit.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/git_commit.dir/build.make src/CMakeFiles/git_commit.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num= "Built target git_commit"
.PHONY : src/CMakeFiles/git_commit.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/git_commit.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/git_commit.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : src/CMakeFiles/git_commit.dir/rule

# Convenience name for target.
git_commit: src/CMakeFiles/git_commit.dir/rule
.PHONY : git_commit

# clean rule for target.
src/CMakeFiles/git_commit.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/git_commit.dir/build.make src/CMakeFiles/git_commit.dir/clean
.PHONY : src/CMakeFiles/git_commit.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/nvshmem_device_project-install.dir

# All Build rule for target.
src/CMakeFiles/nvshmem_device_project-install.dir/all: src/CMakeFiles/nvshmem_device_project-build.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_device_project-install.dir/build.make src/CMakeFiles/nvshmem_device_project-install.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_device_project-install.dir/build.make src/CMakeFiles/nvshmem_device_project-install.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=33 "Built target nvshmem_device_project-install"
.PHONY : src/CMakeFiles/nvshmem_device_project-install.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/nvshmem_device_project-install.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/nvshmem_device_project-install.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : src/CMakeFiles/nvshmem_device_project-install.dir/rule

# Convenience name for target.
nvshmem_device_project-install: src/CMakeFiles/nvshmem_device_project-install.dir/rule
.PHONY : nvshmem_device_project-install

# clean rule for target.
src/CMakeFiles/nvshmem_device_project-install.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_device_project-install.dir/build.make src/CMakeFiles/nvshmem_device_project-install.dir/clean
.PHONY : src/CMakeFiles/nvshmem_device_project-install.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/nvshmem_device_project.dir

# All Build rule for target.
src/CMakeFiles/nvshmem_device_project.dir/all: src/CMakeFiles/nvshmem_device_project-build.dir/all
src/CMakeFiles/nvshmem_device_project.dir/all: src/CMakeFiles/nvshmem_device_project-install.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_device_project.dir/build.make src/CMakeFiles/nvshmem_device_project.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_device_project.dir/build.make src/CMakeFiles/nvshmem_device_project.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num= "Built target nvshmem_device_project"
.PHONY : src/CMakeFiles/nvshmem_device_project.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/nvshmem_device_project.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/nvshmem_device_project.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : src/CMakeFiles/nvshmem_device_project.dir/rule

# Convenience name for target.
nvshmem_device_project: src/CMakeFiles/nvshmem_device_project.dir/rule
.PHONY : nvshmem_device_project

# clean rule for target.
src/CMakeFiles/nvshmem_device_project.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_device_project.dir/build.make src/CMakeFiles/nvshmem_device_project.dir/clean
.PHONY : src/CMakeFiles/nvshmem_device_project.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/nvshmem_bootstrap_pmi.dir

# All Build rule for target.
src/CMakeFiles/nvshmem_bootstrap_pmi.dir/all:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_bootstrap_pmi.dir/build.make src/CMakeFiles/nvshmem_bootstrap_pmi.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_bootstrap_pmi.dir/build.make src/CMakeFiles/nvshmem_bootstrap_pmi.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=26,27 "Built target nvshmem_bootstrap_pmi"
.PHONY : src/CMakeFiles/nvshmem_bootstrap_pmi.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/nvshmem_bootstrap_pmi.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/nvshmem_bootstrap_pmi.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : src/CMakeFiles/nvshmem_bootstrap_pmi.dir/rule

# Convenience name for target.
nvshmem_bootstrap_pmi: src/CMakeFiles/nvshmem_bootstrap_pmi.dir/rule
.PHONY : nvshmem_bootstrap_pmi

# clean rule for target.
src/CMakeFiles/nvshmem_bootstrap_pmi.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_bootstrap_pmi.dir/build.make src/CMakeFiles/nvshmem_bootstrap_pmi.dir/clean
.PHONY : src/CMakeFiles/nvshmem_bootstrap_pmi.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/nvshmem_bootstrap_uid.dir

# All Build rule for target.
src/CMakeFiles/nvshmem_bootstrap_uid.dir/all:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_bootstrap_uid.dir/build.make src/CMakeFiles/nvshmem_bootstrap_uid.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_bootstrap_uid.dir/build.make src/CMakeFiles/nvshmem_bootstrap_uid.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=30 "Built target nvshmem_bootstrap_uid"
.PHONY : src/CMakeFiles/nvshmem_bootstrap_uid.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/nvshmem_bootstrap_uid.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/nvshmem_bootstrap_uid.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : src/CMakeFiles/nvshmem_bootstrap_uid.dir/rule

# Convenience name for target.
nvshmem_bootstrap_uid: src/CMakeFiles/nvshmem_bootstrap_uid.dir/rule
.PHONY : nvshmem_bootstrap_uid

# clean rule for target.
src/CMakeFiles/nvshmem_bootstrap_uid.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_bootstrap_uid.dir/build.make src/CMakeFiles/nvshmem_bootstrap_uid.dir/clean
.PHONY : src/CMakeFiles/nvshmem_bootstrap_uid.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/nvshmem.dir

# All Build rule for target.
src/CMakeFiles/nvshmem.dir/all:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem.dir/build.make src/CMakeFiles/nvshmem.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem.dir/build.make src/CMakeFiles/nvshmem.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25 "Built target nvshmem"
.PHONY : src/CMakeFiles/nvshmem.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/nvshmem.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 23
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/nvshmem.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : src/CMakeFiles/nvshmem.dir/rule

# Convenience name for target.
nvshmem: src/CMakeFiles/nvshmem.dir/rule
.PHONY : nvshmem

# clean rule for target.
src/CMakeFiles/nvshmem.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem.dir/build.make src/CMakeFiles/nvshmem.dir/clean
.PHONY : src/CMakeFiles/nvshmem.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/nvshmem-info.dir

# All Build rule for target.
src/CMakeFiles/nvshmem-info.dir/all: src/CMakeFiles/nvshmem.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem-info.dir/build.make src/CMakeFiles/nvshmem-info.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem-info.dir/build.make src/CMakeFiles/nvshmem-info.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num= "Built target nvshmem-info"
.PHONY : src/CMakeFiles/nvshmem-info.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/nvshmem-info.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 23
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/nvshmem-info.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : src/CMakeFiles/nvshmem-info.dir/rule

# Convenience name for target.
nvshmem-info: src/CMakeFiles/nvshmem-info.dir/rule
.PHONY : nvshmem-info

# clean rule for target.
src/CMakeFiles/nvshmem-info.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem-info.dir/build.make src/CMakeFiles/nvshmem-info.dir/clean
.PHONY : src/CMakeFiles/nvshmem-info.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/package_src_target.dir

# All Build rule for target.
src/CMakeFiles/package_src_target.dir/all:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/package_src_target.dir/build.make src/CMakeFiles/package_src_target.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/package_src_target.dir/build.make src/CMakeFiles/package_src_target.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num= "Built target package_src_target"
.PHONY : src/CMakeFiles/package_src_target.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/package_src_target.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/package_src_target.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : src/CMakeFiles/package_src_target.dir/rule

# Convenience name for target.
package_src_target: src/CMakeFiles/package_src_target.dir/rule
.PHONY : package_src_target

# clean rule for target.
src/CMakeFiles/package_src_target.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/package_src_target.dir/build.make src/CMakeFiles/package_src_target.dir/clean
.PHONY : src/CMakeFiles/package_src_target.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/nvshmem_host.dir

# All Build rule for target.
src/CMakeFiles/nvshmem_host.dir/all: src/CMakeFiles/nvshmem_device_project-build.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_host.dir/build.make src/CMakeFiles/nvshmem_host.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_host.dir/build.make src/CMakeFiles/nvshmem_host.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54 "Built target nvshmem_host"
.PHONY : src/CMakeFiles/nvshmem_host.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/nvshmem_host.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 23
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/nvshmem_host.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : src/CMakeFiles/nvshmem_host.dir/rule

# Convenience name for target.
nvshmem_host: src/CMakeFiles/nvshmem_host.dir/rule
.PHONY : nvshmem_host

# clean rule for target.
src/CMakeFiles/nvshmem_host.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_host.dir/build.make src/CMakeFiles/nvshmem_host.dir/clean
.PHONY : src/CMakeFiles/nvshmem_host.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/nvshmem_transport_libfabric.dir

# All Build rule for target.
src/CMakeFiles/nvshmem_transport_libfabric.dir/all:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_transport_libfabric.dir/build.make src/CMakeFiles/nvshmem_transport_libfabric.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_transport_libfabric.dir/build.make src/CMakeFiles/nvshmem_transport_libfabric.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=56 "Built target nvshmem_transport_libfabric"
.PHONY : src/CMakeFiles/nvshmem_transport_libfabric.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/nvshmem_transport_libfabric.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/nvshmem_transport_libfabric.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : src/CMakeFiles/nvshmem_transport_libfabric.dir/rule

# Convenience name for target.
nvshmem_transport_libfabric: src/CMakeFiles/nvshmem_transport_libfabric.dir/rule
.PHONY : nvshmem_transport_libfabric

# clean rule for target.
src/CMakeFiles/nvshmem_transport_libfabric.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/nvshmem_transport_libfabric.dir/build.make src/CMakeFiles/nvshmem_transport_libfabric.dir/clean
.PHONY : src/CMakeFiles/nvshmem_transport_libfabric.dir/clean

#=============================================================================
# Target rules for target examples/CMakeFiles/collective-launch.dir

# All Build rule for target.
examples/CMakeFiles/collective-launch.dir/all: src/CMakeFiles/nvshmem_host.dir/all
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/collective-launch.dir/build.make examples/CMakeFiles/collective-launch.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/collective-launch.dir/build.make examples/CMakeFiles/collective-launch.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num= "Built target collective-launch"
.PHONY : examples/CMakeFiles/collective-launch.dir/all

# Build rule for subdir invocation for target.
examples/CMakeFiles/collective-launch.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 23
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/collective-launch.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : examples/CMakeFiles/collective-launch.dir/rule

# Convenience name for target.
collective-launch: examples/CMakeFiles/collective-launch.dir/rule
.PHONY : collective-launch

# clean rule for target.
examples/CMakeFiles/collective-launch.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/collective-launch.dir/build.make examples/CMakeFiles/collective-launch.dir/clean
.PHONY : examples/CMakeFiles/collective-launch.dir/clean

#=============================================================================
# Target rules for target examples/CMakeFiles/dev-guide-ring.dir

# All Build rule for target.
examples/CMakeFiles/dev-guide-ring.dir/all: src/CMakeFiles/nvshmem_host.dir/all
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/dev-guide-ring.dir/build.make examples/CMakeFiles/dev-guide-ring.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/dev-guide-ring.dir/build.make examples/CMakeFiles/dev-guide-ring.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=1 "Built target dev-guide-ring"
.PHONY : examples/CMakeFiles/dev-guide-ring.dir/all

# Build rule for subdir invocation for target.
examples/CMakeFiles/dev-guide-ring.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 24
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/dev-guide-ring.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : examples/CMakeFiles/dev-guide-ring.dir/rule

# Convenience name for target.
dev-guide-ring: examples/CMakeFiles/dev-guide-ring.dir/rule
.PHONY : dev-guide-ring

# clean rule for target.
examples/CMakeFiles/dev-guide-ring.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/dev-guide-ring.dir/build.make examples/CMakeFiles/dev-guide-ring.dir/clean
.PHONY : examples/CMakeFiles/dev-guide-ring.dir/clean

#=============================================================================
# Target rules for target examples/CMakeFiles/on-stream.dir

# All Build rule for target.
examples/CMakeFiles/on-stream.dir/all: src/CMakeFiles/nvshmem_host.dir/all
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/on-stream.dir/build.make examples/CMakeFiles/on-stream.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/on-stream.dir/build.make examples/CMakeFiles/on-stream.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=57 "Built target on-stream"
.PHONY : examples/CMakeFiles/on-stream.dir/all

# Build rule for subdir invocation for target.
examples/CMakeFiles/on-stream.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 24
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/on-stream.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : examples/CMakeFiles/on-stream.dir/rule

# Convenience name for target.
on-stream: examples/CMakeFiles/on-stream.dir/rule
.PHONY : on-stream

# clean rule for target.
examples/CMakeFiles/on-stream.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/on-stream.dir/build.make examples/CMakeFiles/on-stream.dir/clean
.PHONY : examples/CMakeFiles/on-stream.dir/clean

#=============================================================================
# Target rules for target examples/CMakeFiles/thread-group.dir

# All Build rule for target.
examples/CMakeFiles/thread-group.dir/all: src/CMakeFiles/nvshmem_host.dir/all
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/thread-group.dir/build.make examples/CMakeFiles/thread-group.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/thread-group.dir/build.make examples/CMakeFiles/thread-group.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=99 "Built target thread-group"
.PHONY : examples/CMakeFiles/thread-group.dir/all

# Build rule for subdir invocation for target.
examples/CMakeFiles/thread-group.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 24
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/thread-group.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : examples/CMakeFiles/thread-group.dir/rule

# Convenience name for target.
thread-group: examples/CMakeFiles/thread-group.dir/rule
.PHONY : thread-group

# clean rule for target.
examples/CMakeFiles/thread-group.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/thread-group.dir/build.make examples/CMakeFiles/thread-group.dir/clean
.PHONY : examples/CMakeFiles/thread-group.dir/clean

#=============================================================================
# Target rules for target examples/CMakeFiles/put-block.dir

# All Build rule for target.
examples/CMakeFiles/put-block.dir/all: src/CMakeFiles/nvshmem_host.dir/all
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/put-block.dir/build.make examples/CMakeFiles/put-block.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/put-block.dir/build.make examples/CMakeFiles/put-block.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=96 "Built target put-block"
.PHONY : examples/CMakeFiles/put-block.dir/all

# Build rule for subdir invocation for target.
examples/CMakeFiles/put-block.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 24
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/put-block.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : examples/CMakeFiles/put-block.dir/rule

# Convenience name for target.
put-block: examples/CMakeFiles/put-block.dir/rule
.PHONY : put-block

# clean rule for target.
examples/CMakeFiles/put-block.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/put-block.dir/build.make examples/CMakeFiles/put-block.dir/clean
.PHONY : examples/CMakeFiles/put-block.dir/clean

#=============================================================================
# Target rules for target examples/CMakeFiles/ring-bcast.dir

# All Build rule for target.
examples/CMakeFiles/ring-bcast.dir/all: src/CMakeFiles/nvshmem_host.dir/all
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/ring-bcast.dir/build.make examples/CMakeFiles/ring-bcast.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/ring-bcast.dir/build.make examples/CMakeFiles/ring-bcast.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=97 "Built target ring-bcast"
.PHONY : examples/CMakeFiles/ring-bcast.dir/all

# Build rule for subdir invocation for target.
examples/CMakeFiles/ring-bcast.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 24
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/ring-bcast.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : examples/CMakeFiles/ring-bcast.dir/rule

# Convenience name for target.
ring-bcast: examples/CMakeFiles/ring-bcast.dir/rule
.PHONY : ring-bcast

# clean rule for target.
examples/CMakeFiles/ring-bcast.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/ring-bcast.dir/build.make examples/CMakeFiles/ring-bcast.dir/clean
.PHONY : examples/CMakeFiles/ring-bcast.dir/clean

#=============================================================================
# Target rules for target examples/CMakeFiles/ring-reduce.dir

# All Build rule for target.
examples/CMakeFiles/ring-reduce.dir/all: src/CMakeFiles/nvshmem_host.dir/all
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/ring-reduce.dir/build.make examples/CMakeFiles/ring-reduce.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/ring-reduce.dir/build.make examples/CMakeFiles/ring-reduce.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=98 "Built target ring-reduce"
.PHONY : examples/CMakeFiles/ring-reduce.dir/all

# Build rule for subdir invocation for target.
examples/CMakeFiles/ring-reduce.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 24
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/ring-reduce.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : examples/CMakeFiles/ring-reduce.dir/rule

# Convenience name for target.
ring-reduce: examples/CMakeFiles/ring-reduce.dir/rule
.PHONY : ring-reduce

# clean rule for target.
examples/CMakeFiles/ring-reduce.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/ring-reduce.dir/build.make examples/CMakeFiles/ring-reduce.dir/clean
.PHONY : examples/CMakeFiles/ring-reduce.dir/clean

#=============================================================================
# Target rules for target examples/CMakeFiles/moe_shuffle.dir

# All Build rule for target.
examples/CMakeFiles/moe_shuffle.dir/all: src/CMakeFiles/nvshmem_host.dir/all
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/moe_shuffle.dir/build.make examples/CMakeFiles/moe_shuffle.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/moe_shuffle.dir/build.make examples/CMakeFiles/moe_shuffle.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=2 "Built target moe_shuffle"
.PHONY : examples/CMakeFiles/moe_shuffle.dir/all

# Build rule for subdir invocation for target.
examples/CMakeFiles/moe_shuffle.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 24
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/moe_shuffle.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : examples/CMakeFiles/moe_shuffle.dir/rule

# Convenience name for target.
moe_shuffle: examples/CMakeFiles/moe_shuffle.dir/rule
.PHONY : moe_shuffle

# clean rule for target.
examples/CMakeFiles/moe_shuffle.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/moe_shuffle.dir/build.make examples/CMakeFiles/moe_shuffle.dir/clean
.PHONY : examples/CMakeFiles/moe_shuffle.dir/clean

#=============================================================================
# Target rules for target examples/CMakeFiles/user-buffer.dir

# All Build rule for target.
examples/CMakeFiles/user-buffer.dir/all: src/CMakeFiles/nvshmem_host.dir/all
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/user-buffer.dir/build.make examples/CMakeFiles/user-buffer.dir/depend
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/user-buffer.dir/build.make examples/CMakeFiles/user-buffer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=100 "Built target user-buffer"
.PHONY : examples/CMakeFiles/user-buffer.dir/all

# Build rule for subdir invocation for target.
examples/CMakeFiles/user-buffer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 24
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/user-buffer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : examples/CMakeFiles/user-buffer.dir/rule

# Convenience name for target.
user-buffer: examples/CMakeFiles/user-buffer.dir/rule
.PHONY : user-buffer

# clean rule for target.
examples/CMakeFiles/user-buffer.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/user-buffer.dir/build.make examples/CMakeFiles/user-buffer.dir/clean
.PHONY : examples/CMakeFiles/user-buffer.dir/clean

#=============================================================================
# Target rules for target perftest/common/CMakeFiles/nvshmem_perftest_helper.dir

# All Build rule for target.
perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all: src/CMakeFiles/nvshmem_host.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/build.make perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/build.make perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=55 "Built target nvshmem_perftest_helper"
.PHONY : perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all

# Build rule for subdir invocation for target.
perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 24
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/rule

# Convenience name for target.
nvshmem_perftest_helper: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/rule
.PHONY : nvshmem_perftest_helper

# clean rule for target.
perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/build.make perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/clean
.PHONY : perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/clean

#=============================================================================
# Target rules for target perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir

# All Build rule for target.
perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/build.make perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/build.make perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=81 "Built target perf_device_tile_allgather_latency"
.PHONY : perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/rule

# Convenience name for target.
perf_device_tile_allgather_latency: perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/rule
.PHONY : perf_device_tile_allgather_latency

# clean rule for target.
perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/build.make perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/clean
.PHONY : perftest/device/tile/CMakeFiles/perf_device_tile_allgather_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir

# All Build rule for target.
perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/build.make perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/build.make perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=82 "Built target perf_device_tile_allreduce_latency"
.PHONY : perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/rule

# Convenience name for target.
perf_device_tile_allreduce_latency: perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/rule
.PHONY : perf_device_tile_allreduce_latency

# clean rule for target.
perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/build.make perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/clean
.PHONY : perftest/device/tile/CMakeFiles/perf_device_tile_allreduce_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir

# All Build rule for target.
perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=58 "Built target perf_device_alltoall_latency"
.PHONY : perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/rule

# Convenience name for target.
perf_device_alltoall_latency: perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/rule
.PHONY : perf_device_alltoall_latency

# clean rule for target.
perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/clean
.PHONY : perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir

# All Build rule for target.
perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=61 "Built target perf_device_fcollect_latency"
.PHONY : perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/rule

# Convenience name for target.
perf_device_fcollect_latency: perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/rule
.PHONY : perf_device_fcollect_latency

# clean rule for target.
perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/clean
.PHONY : perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir

# All Build rule for target.
perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=60 "Built target perf_device_bcast_latency"
.PHONY : perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/rule

# Convenience name for target.
perf_device_bcast_latency: perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/rule
.PHONY : perf_device_bcast_latency

# clean rule for target.
perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/clean
.PHONY : perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir

# All Build rule for target.
perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=63 "Built target perf_device_reduction_latency"
.PHONY : perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/rule

# Convenience name for target.
perf_device_reduction_latency: perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/rule
.PHONY : perf_device_reduction_latency

# clean rule for target.
perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/clean
.PHONY : perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir

# All Build rule for target.
perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=62 "Built target perf_device_reducescatter_latency"
.PHONY : perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/rule

# Convenience name for target.
perf_device_reducescatter_latency: perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/rule
.PHONY : perf_device_reducescatter_latency

# clean rule for target.
perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/clean
.PHONY : perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir

# All Build rule for target.
perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=59 "Built target perf_device_barrier_latency"
.PHONY : perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/rule

# Convenience name for target.
perf_device_barrier_latency: perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/rule
.PHONY : perf_device_barrier_latency

# clean rule for target.
perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/clean
.PHONY : perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir

# All Build rule for target.
perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=80 "Built target perf_device_sync_latency"
.PHONY : perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/rule

# Convenience name for target.
perf_device_sync_latency: perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/rule
.PHONY : perf_device_sync_latency

# clean rule for target.
perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/clean
.PHONY : perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=79 "Built target perf_device_shmem_st_bw"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/rule

# Convenience name for target.
perf_device_shmem_st_bw: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/rule
.PHONY : perf_device_shmem_st_bw

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=76 "Built target perf_device_shmem_put_ping_pong_latency"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/rule

# Convenience name for target.
perf_device_shmem_put_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/rule
.PHONY : perf_device_shmem_put_ping_pong_latency

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=64 "Built target perf_device_shmem_atomic_bw"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/rule

# Convenience name for target.
perf_device_shmem_atomic_bw: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/rule
.PHONY : perf_device_shmem_atomic_bw

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=71 "Built target perf_device_shmem_p_bw"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/rule

# Convenience name for target.
perf_device_shmem_p_bw: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/rule
.PHONY : perf_device_shmem_p_bw

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=77 "Built target perf_device_shmem_put_signal_ping_pong_latency"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/rule

# Convenience name for target.
perf_device_shmem_put_signal_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/rule
.PHONY : perf_device_shmem_put_signal_ping_pong_latency

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num= "Built target perf_device_shmem_put_latency"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 24
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/rule

# Convenience name for target.
perf_device_shmem_put_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/rule
.PHONY : perf_device_shmem_put_latency

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=65 "Built target perf_device_shmem_atomic_latency"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/rule

# Convenience name for target.
perf_device_shmem_atomic_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/rule
.PHONY : perf_device_shmem_atomic_latency

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=66 "Built target perf_device_shmem_atomic_ping_pong_latency"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/rule

# Convenience name for target.
perf_device_shmem_atomic_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/rule
.PHONY : perf_device_shmem_atomic_ping_pong_latency

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=67 "Built target perf_device_shmem_g_bw"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/rule

# Convenience name for target.
perf_device_shmem_g_bw: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/rule
.PHONY : perf_device_shmem_g_bw

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=68 "Built target perf_device_shmem_g_latency"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/rule

# Convenience name for target.
perf_device_shmem_g_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/rule
.PHONY : perf_device_shmem_g_latency

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=73 "Built target perf_device_shmem_p_ping_pong_latency"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/rule

# Convenience name for target.
perf_device_shmem_p_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/rule
.PHONY : perf_device_shmem_p_ping_pong_latency

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=69 "Built target perf_device_shmem_get_bw"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/rule

# Convenience name for target.
perf_device_shmem_get_bw: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/rule
.PHONY : perf_device_shmem_get_bw

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=70 "Built target perf_device_shmem_get_latency"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/rule

# Convenience name for target.
perf_device_shmem_get_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/rule
.PHONY : perf_device_shmem_get_latency

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=78 "Built target perf_device_shmem_signal_ping_pong_latency"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/rule

# Convenience name for target.
perf_device_shmem_signal_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/rule
.PHONY : perf_device_shmem_signal_ping_pong_latency

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=72 "Built target perf_device_shmem_p_latency"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/rule

# Convenience name for target.
perf_device_shmem_p_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/rule
.PHONY : perf_device_shmem_p_latency

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=74 "Built target perf_device_shmem_put_atomic_ping_pong_latency"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/rule

# Convenience name for target.
perf_device_shmem_put_atomic_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/rule
.PHONY : perf_device_shmem_put_atomic_ping_pong_latency

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/clean

#=============================================================================
# Target rules for target perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir

# All Build rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=75 "Built target perf_device_shmem_put_bw"
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/all

# Build rule for subdir invocation for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/rule

# Convenience name for target.
perf_device_shmem_put_bw: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/rule
.PHONY : perf_device_shmem_put_bw

# clean rule for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/clean
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/clean

#=============================================================================
# Target rules for target perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir

# All Build rule for target.
perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=84 "Built target perf_host_barrier_all_on_stream"
.PHONY : perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/all

# Build rule for subdir invocation for target.
perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/rule

# Convenience name for target.
perf_host_barrier_all_on_stream: perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/rule
.PHONY : perf_host_barrier_all_on_stream

# clean rule for target.
perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/clean
.PHONY : perftest/host/coll/CMakeFiles/perf_host_barrier_all_on_stream.dir/clean

#=============================================================================
# Target rules for target perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir

# All Build rule for target.
perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=92 "Built target perf_host_reduction_on_stream"
.PHONY : perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/all

# Build rule for subdir invocation for target.
perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/rule

# Convenience name for target.
perf_host_reduction_on_stream: perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/rule
.PHONY : perf_host_reduction_on_stream

# clean rule for target.
perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/clean
.PHONY : perftest/host/coll/CMakeFiles/perf_host_reduction_on_stream.dir/clean

#=============================================================================
# Target rules for target perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir

# All Build rule for target.
perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=83 "Built target perf_host_alltoall_on_stream"
.PHONY : perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/all

# Build rule for subdir invocation for target.
perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/rule

# Convenience name for target.
perf_host_alltoall_on_stream: perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/rule
.PHONY : perf_host_alltoall_on_stream

# clean rule for target.
perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/clean
.PHONY : perftest/host/coll/CMakeFiles/perf_host_alltoall_on_stream.dir/clean

#=============================================================================
# Target rules for target perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir

# All Build rule for target.
perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=95 "Built target perf_host_sync_on_stream"
.PHONY : perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/all

# Build rule for subdir invocation for target.
perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/rule

# Convenience name for target.
perf_host_sync_on_stream: perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/rule
.PHONY : perf_host_sync_on_stream

# clean rule for target.
perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/clean
.PHONY : perftest/host/coll/CMakeFiles/perf_host_sync_on_stream.dir/clean

#=============================================================================
# Target rules for target perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir

# All Build rule for target.
perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=85 "Built target perf_host_barrier_on_stream"
.PHONY : perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/all

# Build rule for subdir invocation for target.
perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/rule

# Convenience name for target.
perf_host_barrier_on_stream: perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/rule
.PHONY : perf_host_barrier_on_stream

# clean rule for target.
perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/clean
.PHONY : perftest/host/coll/CMakeFiles/perf_host_barrier_on_stream.dir/clean

#=============================================================================
# Target rules for target perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir

# All Build rule for target.
perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=86 "Built target perf_host_broadcast_on_stream"
.PHONY : perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/all

# Build rule for subdir invocation for target.
perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/rule

# Convenience name for target.
perf_host_broadcast_on_stream: perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/rule
.PHONY : perf_host_broadcast_on_stream

# clean rule for target.
perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/clean
.PHONY : perftest/host/coll/CMakeFiles/perf_host_broadcast_on_stream.dir/clean

#=============================================================================
# Target rules for target perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir

# All Build rule for target.
perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=88 "Built target perf_host_fcollect_on_stream"
.PHONY : perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/all

# Build rule for subdir invocation for target.
perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/rule

# Convenience name for target.
perf_host_fcollect_on_stream: perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/rule
.PHONY : perf_host_fcollect_on_stream

# clean rule for target.
perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/clean
.PHONY : perftest/host/coll/CMakeFiles/perf_host_fcollect_on_stream.dir/clean

#=============================================================================
# Target rules for target perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir

# All Build rule for target.
perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=91 "Built target perf_host_reducescatter_on_stream"
.PHONY : perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/all

# Build rule for subdir invocation for target.
perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/rule

# Convenience name for target.
perf_host_reducescatter_on_stream: perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/rule
.PHONY : perf_host_reducescatter_on_stream

# clean rule for target.
perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/clean
.PHONY : perftest/host/coll/CMakeFiles/perf_host_reducescatter_on_stream.dir/clean

#=============================================================================
# Target rules for target perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir

# All Build rule for target.
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=94 "Built target perf_host_sync_all_on_stream"
.PHONY : perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/all

# Build rule for subdir invocation for target.
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/rule

# Convenience name for target.
perf_host_sync_all_on_stream: perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/rule
.PHONY : perf_host_sync_all_on_stream

# clean rule for target.
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/build.make perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/clean
.PHONY : perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/clean

#=============================================================================
# Target rules for target perftest/host/init/CMakeFiles/perf_host_malloc.dir

# All Build rule for target.
perftest/host/init/CMakeFiles/perf_host_malloc.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/host/init/CMakeFiles/perf_host_malloc.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/host/init/CMakeFiles/perf_host_malloc.dir/build.make perftest/host/init/CMakeFiles/perf_host_malloc.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/host/init/CMakeFiles/perf_host_malloc.dir/build.make perftest/host/init/CMakeFiles/perf_host_malloc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=90 "Built target perf_host_malloc"
.PHONY : perftest/host/init/CMakeFiles/perf_host_malloc.dir/all

# Build rule for subdir invocation for target.
perftest/host/init/CMakeFiles/perf_host_malloc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/host/init/CMakeFiles/perf_host_malloc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/host/init/CMakeFiles/perf_host_malloc.dir/rule

# Convenience name for target.
perf_host_malloc: perftest/host/init/CMakeFiles/perf_host_malloc.dir/rule
.PHONY : perf_host_malloc

# clean rule for target.
perftest/host/init/CMakeFiles/perf_host_malloc.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/host/init/CMakeFiles/perf_host_malloc.dir/build.make perftest/host/init/CMakeFiles/perf_host_malloc.dir/clean
.PHONY : perftest/host/init/CMakeFiles/perf_host_malloc.dir/clean

#=============================================================================
# Target rules for target perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir

# All Build rule for target.
perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/build.make perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/build.make perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=93 "Built target perf_host_stream_latency"
.PHONY : perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/all

# Build rule for subdir invocation for target.
perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/rule

# Convenience name for target.
perf_host_stream_latency: perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/rule
.PHONY : perf_host_stream_latency

# clean rule for target.
perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/build.make perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/clean
.PHONY : perftest/host/pt-to-pt/CMakeFiles/perf_host_stream_latency.dir/clean

#=============================================================================
# Target rules for target perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir

# All Build rule for target.
perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/build.make perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/build.make perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=89 "Built target perf_host_latency"
.PHONY : perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/all

# Build rule for subdir invocation for target.
perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/rule

# Convenience name for target.
perf_host_latency: perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/rule
.PHONY : perf_host_latency

# clean rule for target.
perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/build.make perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/clean
.PHONY : perftest/host/pt-to-pt/CMakeFiles/perf_host_latency.dir/clean

#=============================================================================
# Target rules for target perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir

# All Build rule for target.
perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/all: src/CMakeFiles/nvshmem_host.dir/all
perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/all: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/all
	$(MAKE) $(MAKESILENT) -f perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/build.make perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/depend
	$(MAKE) $(MAKESILENT) -f perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/build.make perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=87 "Built target perf_host_bw"
.PHONY : perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/all

# Build rule for subdir invocation for target.
perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/rule

# Convenience name for target.
perf_host_bw: perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/rule
.PHONY : perf_host_bw

# clean rule for target.
perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/clean:
	$(MAKE) $(MAKESILENT) -f perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/build.make perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/clean
.PHONY : perftest/host/pt-to-pt/CMakeFiles/perf_host_bw.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

