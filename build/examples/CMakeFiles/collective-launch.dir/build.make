# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build

# Include any dependencies generated for this target.
include examples/CMakeFiles/collective-launch.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include examples/CMakeFiles/collective-launch.dir/compiler_depend.make

# Include the progress variables for this target.
include examples/CMakeFiles/collective-launch.dir/progress.make

# Include the compile flags for this target's objects.
include examples/CMakeFiles/collective-launch.dir/flags.make

examples/CMakeFiles/collective-launch.dir/collective-launch.cu.o: examples/CMakeFiles/collective-launch.dir/flags.make
examples/CMakeFiles/collective-launch.dir/collective-launch.cu.o: ../examples/collective-launch.cu
examples/CMakeFiles/collective-launch.dir/collective-launch.cu.o: examples/CMakeFiles/collective-launch.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CUDA object examples/CMakeFiles/collective-launch.dir/collective-launch.cu.o"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples && /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/nvcc -forward-unknown-to-host-compiler $(CUDA_DEFINES) $(CUDA_INCLUDES) $(CUDA_FLAGS) -MD -MT examples/CMakeFiles/collective-launch.dir/collective-launch.cu.o -MF CMakeFiles/collective-launch.dir/collective-launch.cu.o.d -x cu -dc /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/collective-launch.cu -o CMakeFiles/collective-launch.dir/collective-launch.cu.o

examples/CMakeFiles/collective-launch.dir/collective-launch.cu.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CUDA source to CMakeFiles/collective-launch.dir/collective-launch.cu.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_PREPROCESSED_SOURCE

examples/CMakeFiles/collective-launch.dir/collective-launch.cu.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CUDA source to assembly CMakeFiles/collective-launch.dir/collective-launch.cu.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_ASSEMBLY_SOURCE

# Object files for target collective-launch
collective__launch_OBJECTS = \
"CMakeFiles/collective-launch.dir/collective-launch.cu.o"

# External object files for target collective-launch
collective__launch_EXTERNAL_OBJECTS =

examples/CMakeFiles/collective-launch.dir/cmake_device_link.o: examples/CMakeFiles/collective-launch.dir/collective-launch.cu.o
examples/CMakeFiles/collective-launch.dir/cmake_device_link.o: examples/CMakeFiles/collective-launch.dir/build.make
examples/CMakeFiles/collective-launch.dir/cmake_device_link.o: /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/lib64/libcudart.so
examples/CMakeFiles/collective-launch.dir/cmake_device_link.o: /usr/lib64/libcuda.so
examples/CMakeFiles/collective-launch.dir/cmake_device_link.o: src/lib/libnvshmem_host.so.3.3.9
examples/CMakeFiles/collective-launch.dir/cmake_device_link.o: src/lib/libnvshmem_device.a
examples/CMakeFiles/collective-launch.dir/cmake_device_link.o: examples/CMakeFiles/collective-launch.dir/dlink.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CUDA device code CMakeFiles/collective-launch.dir/cmake_device_link.o"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/collective-launch.dir/dlink.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
examples/CMakeFiles/collective-launch.dir/build: examples/CMakeFiles/collective-launch.dir/cmake_device_link.o
.PHONY : examples/CMakeFiles/collective-launch.dir/build

# Object files for target collective-launch
collective__launch_OBJECTS = \
"CMakeFiles/collective-launch.dir/collective-launch.cu.o"

# External object files for target collective-launch
collective__launch_EXTERNAL_OBJECTS =

examples/collective-launch: examples/CMakeFiles/collective-launch.dir/collective-launch.cu.o
examples/collective-launch: examples/CMakeFiles/collective-launch.dir/build.make
examples/collective-launch: /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/lib64/libcudart.so
examples/collective-launch: /usr/lib64/libcuda.so
examples/collective-launch: src/lib/libnvshmem_host.so.3.3.9
examples/collective-launch: src/lib/libnvshmem_device.a
examples/collective-launch: examples/CMakeFiles/collective-launch.dir/cmake_device_link.o
examples/collective-launch: examples/CMakeFiles/collective-launch.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CUDA executable collective-launch"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/collective-launch.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
examples/CMakeFiles/collective-launch.dir/build: examples/collective-launch
.PHONY : examples/CMakeFiles/collective-launch.dir/build

examples/CMakeFiles/collective-launch.dir/clean:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples && $(CMAKE_COMMAND) -P CMakeFiles/collective-launch.dir/cmake_clean.cmake
.PHONY : examples/CMakeFiles/collective-launch.dir/clean

examples/CMakeFiles/collective-launch.dir/depend:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9 /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/CMakeFiles/collective-launch.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : examples/CMakeFiles/collective-launch.dir/depend

