# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build

# Include any dependencies generated for this target.
include examples/CMakeFiles/mpi-based-init.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include examples/CMakeFiles/mpi-based-init.dir/compiler_depend.make

# Include the progress variables for this target.
include examples/CMakeFiles/mpi-based-init.dir/progress.make

# Include the compile flags for this target's objects.
include examples/CMakeFiles/mpi-based-init.dir/flags.make

examples/CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.o: examples/CMakeFiles/mpi-based-init.dir/flags.make
examples/CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.o: ../examples/mpi-based-init.cu
examples/CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.o: examples/CMakeFiles/mpi-based-init.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CUDA object examples/CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.o"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples && /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/nvcc -forward-unknown-to-host-compiler $(CUDA_DEFINES) $(CUDA_INCLUDES) $(CUDA_FLAGS) -MD -MT examples/CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.o -MF CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.o.d -x cu -dc /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/mpi-based-init.cu -o CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.o

examples/CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CUDA source to CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_PREPROCESSED_SOURCE

examples/CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CUDA source to assembly CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_ASSEMBLY_SOURCE

# Object files for target mpi-based-init
mpi__based__init_OBJECTS = \
"CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.o"

# External object files for target mpi-based-init
mpi__based__init_EXTERNAL_OBJECTS =

examples/CMakeFiles/mpi-based-init.dir/cmake_device_link.o: examples/CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.o
examples/CMakeFiles/mpi-based-init.dir/cmake_device_link.o: examples/CMakeFiles/mpi-based-init.dir/build.make
examples/CMakeFiles/mpi-based-init.dir/cmake_device_link.o: src/lib/libnvshmem_host.so.3.3.9
examples/CMakeFiles/mpi-based-init.dir/cmake_device_link.o: src/lib/libnvshmem_device.a
examples/CMakeFiles/mpi-based-init.dir/cmake_device_link.o: /opt/cray/pe/mpich/8.1.30/ofi/gnu/12.3/lib/libmpi_gnu_123.so
examples/CMakeFiles/mpi-based-init.dir/cmake_device_link.o: /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/lib64/libcudart.so
examples/CMakeFiles/mpi-based-init.dir/cmake_device_link.o: /usr/lib64/libcuda.so
examples/CMakeFiles/mpi-based-init.dir/cmake_device_link.o: examples/CMakeFiles/mpi-based-init.dir/dlink.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CUDA device code CMakeFiles/mpi-based-init.dir/cmake_device_link.o"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/mpi-based-init.dir/dlink.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
examples/CMakeFiles/mpi-based-init.dir/build: examples/CMakeFiles/mpi-based-init.dir/cmake_device_link.o
.PHONY : examples/CMakeFiles/mpi-based-init.dir/build

# Object files for target mpi-based-init
mpi__based__init_OBJECTS = \
"CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.o"

# External object files for target mpi-based-init
mpi__based__init_EXTERNAL_OBJECTS =

examples/mpi-based-init: examples/CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.o
examples/mpi-based-init: examples/CMakeFiles/mpi-based-init.dir/build.make
examples/mpi-based-init: src/lib/libnvshmem_host.so.3.3.9
examples/mpi-based-init: src/lib/libnvshmem_device.a
examples/mpi-based-init: /opt/cray/pe/mpich/8.1.30/ofi/gnu/12.3/lib/libmpi_gnu_123.so
examples/mpi-based-init: /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/lib64/libcudart.so
examples/mpi-based-init: /usr/lib64/libcuda.so
examples/mpi-based-init: examples/CMakeFiles/mpi-based-init.dir/cmake_device_link.o
examples/mpi-based-init: examples/CMakeFiles/mpi-based-init.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CUDA executable mpi-based-init"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/mpi-based-init.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
examples/CMakeFiles/mpi-based-init.dir/build: examples/mpi-based-init
.PHONY : examples/CMakeFiles/mpi-based-init.dir/build

examples/CMakeFiles/mpi-based-init.dir/clean:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples && $(CMAKE_COMMAND) -P CMakeFiles/mpi-based-init.dir/cmake_clean.cmake
.PHONY : examples/CMakeFiles/mpi-based-init.dir/clean

examples/CMakeFiles/mpi-based-init.dir/depend:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9 /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/CMakeFiles/mpi-based-init.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : examples/CMakeFiles/mpi-based-init.dir/depend

