/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/nvcc -forward-unknown-to-host-compiler -O3 -DNDEBUG --generate-code=arch=compute_80,code=[compute_80,sm_80] -Xcompiler=-fPIC -Wno-deprecated-gpu-targets -shared -dlink CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.o -o CMakeFiles/mpi-based-init.dir/cmake_device_link.o  ../src/lib/libnvshmem_device.a  -lcudadevrt -lcudart_static -lrt -lpthread -ldl 
