/opt/cray/pe/gcc-native/13/bin/g++ -Wl,--enable-new-dtags CMakeFiles/mpi-based-init.dir/mpi-based-init.cu.o CMakeFiles/mpi-based-init.dir/cmake_device_link.o -o mpi-based-init  -Wl,-rpath,"\$ORIGIN/../../lib" ../src/lib/libnvshmem_host.so.3.3.9 ../src/lib/libnvshmem_device.a /opt/cray/pe/mpich/8.1.30/ofi/gnu/12.3/lib/libmpi_gnu_123.so /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/lib64/libcudart.so /usr/lib64/libcuda.so -lcudadevrt -lcudart_static -lrt -lpthread -ldl  -L"/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs" -L"/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib"
