# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build

# Include any dependencies generated for this target.
include examples/CMakeFiles/on-stream.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include examples/CMakeFiles/on-stream.dir/compiler_depend.make

# Include the progress variables for this target.
include examples/CMakeFiles/on-stream.dir/progress.make

# Include the compile flags for this target's objects.
include examples/CMakeFiles/on-stream.dir/flags.make

examples/CMakeFiles/on-stream.dir/on-stream.cu.o: examples/CMakeFiles/on-stream.dir/flags.make
examples/CMakeFiles/on-stream.dir/on-stream.cu.o: ../examples/on-stream.cu
examples/CMakeFiles/on-stream.dir/on-stream.cu.o: examples/CMakeFiles/on-stream.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CUDA object examples/CMakeFiles/on-stream.dir/on-stream.cu.o"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples && /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/nvcc -forward-unknown-to-host-compiler $(CUDA_DEFINES) $(CUDA_INCLUDES) $(CUDA_FLAGS) -MD -MT examples/CMakeFiles/on-stream.dir/on-stream.cu.o -MF CMakeFiles/on-stream.dir/on-stream.cu.o.d -x cu -dc /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/on-stream.cu -o CMakeFiles/on-stream.dir/on-stream.cu.o

examples/CMakeFiles/on-stream.dir/on-stream.cu.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CUDA source to CMakeFiles/on-stream.dir/on-stream.cu.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_PREPROCESSED_SOURCE

examples/CMakeFiles/on-stream.dir/on-stream.cu.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CUDA source to assembly CMakeFiles/on-stream.dir/on-stream.cu.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_ASSEMBLY_SOURCE

# Object files for target on-stream
on__stream_OBJECTS = \
"CMakeFiles/on-stream.dir/on-stream.cu.o"

# External object files for target on-stream
on__stream_EXTERNAL_OBJECTS =

examples/CMakeFiles/on-stream.dir/cmake_device_link.o: examples/CMakeFiles/on-stream.dir/on-stream.cu.o
examples/CMakeFiles/on-stream.dir/cmake_device_link.o: examples/CMakeFiles/on-stream.dir/build.make
examples/CMakeFiles/on-stream.dir/cmake_device_link.o: /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/lib64/libcudart.so
examples/CMakeFiles/on-stream.dir/cmake_device_link.o: /usr/lib64/libcuda.so
examples/CMakeFiles/on-stream.dir/cmake_device_link.o: src/lib/libnvshmem_host.so.3.3.9
examples/CMakeFiles/on-stream.dir/cmake_device_link.o: src/lib/libnvshmem_device.a
examples/CMakeFiles/on-stream.dir/cmake_device_link.o: examples/CMakeFiles/on-stream.dir/dlink.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CUDA device code CMakeFiles/on-stream.dir/cmake_device_link.o"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/on-stream.dir/dlink.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
examples/CMakeFiles/on-stream.dir/build: examples/CMakeFiles/on-stream.dir/cmake_device_link.o
.PHONY : examples/CMakeFiles/on-stream.dir/build

# Object files for target on-stream
on__stream_OBJECTS = \
"CMakeFiles/on-stream.dir/on-stream.cu.o"

# External object files for target on-stream
on__stream_EXTERNAL_OBJECTS =

examples/on-stream: examples/CMakeFiles/on-stream.dir/on-stream.cu.o
examples/on-stream: examples/CMakeFiles/on-stream.dir/build.make
examples/on-stream: /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/lib64/libcudart.so
examples/on-stream: /usr/lib64/libcuda.so
examples/on-stream: src/lib/libnvshmem_host.so.3.3.9
examples/on-stream: src/lib/libnvshmem_device.a
examples/on-stream: examples/CMakeFiles/on-stream.dir/cmake_device_link.o
examples/on-stream: examples/CMakeFiles/on-stream.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CUDA executable on-stream"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/on-stream.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
examples/CMakeFiles/on-stream.dir/build: examples/on-stream
.PHONY : examples/CMakeFiles/on-stream.dir/build

examples/CMakeFiles/on-stream.dir/clean:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples && $(CMAKE_COMMAND) -P CMakeFiles/on-stream.dir/cmake_clean.cmake
.PHONY : examples/CMakeFiles/on-stream.dir/clean

examples/CMakeFiles/on-stream.dir/depend:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9 /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/CMakeFiles/on-stream.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : examples/CMakeFiles/on-stream.dir/depend

