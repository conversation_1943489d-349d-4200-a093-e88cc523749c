# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && /usr/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package
.PHONY : package/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && /usr/bin/cpack --config ./CPackSourceConfig.cmake /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source
.PHONY : package_source/fast

# The main all target
all: cmake_check_build_system
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples//CMakeFiles/progress.marks
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
examples/CMakeFiles/collective-launch.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/collective-launch.dir/rule
.PHONY : examples/CMakeFiles/collective-launch.dir/rule

# Convenience name for target.
collective-launch: examples/CMakeFiles/collective-launch.dir/rule
.PHONY : collective-launch

# fast build rule for target.
collective-launch/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/collective-launch.dir/build.make examples/CMakeFiles/collective-launch.dir/build
.PHONY : collective-launch/fast

# Convenience name for target.
examples/CMakeFiles/dev-guide-ring.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/dev-guide-ring.dir/rule
.PHONY : examples/CMakeFiles/dev-guide-ring.dir/rule

# Convenience name for target.
dev-guide-ring: examples/CMakeFiles/dev-guide-ring.dir/rule
.PHONY : dev-guide-ring

# fast build rule for target.
dev-guide-ring/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/dev-guide-ring.dir/build.make examples/CMakeFiles/dev-guide-ring.dir/build
.PHONY : dev-guide-ring/fast

# Convenience name for target.
examples/CMakeFiles/on-stream.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/on-stream.dir/rule
.PHONY : examples/CMakeFiles/on-stream.dir/rule

# Convenience name for target.
on-stream: examples/CMakeFiles/on-stream.dir/rule
.PHONY : on-stream

# fast build rule for target.
on-stream/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/on-stream.dir/build.make examples/CMakeFiles/on-stream.dir/build
.PHONY : on-stream/fast

# Convenience name for target.
examples/CMakeFiles/thread-group.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/thread-group.dir/rule
.PHONY : examples/CMakeFiles/thread-group.dir/rule

# Convenience name for target.
thread-group: examples/CMakeFiles/thread-group.dir/rule
.PHONY : thread-group

# fast build rule for target.
thread-group/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/thread-group.dir/build.make examples/CMakeFiles/thread-group.dir/build
.PHONY : thread-group/fast

# Convenience name for target.
examples/CMakeFiles/put-block.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/put-block.dir/rule
.PHONY : examples/CMakeFiles/put-block.dir/rule

# Convenience name for target.
put-block: examples/CMakeFiles/put-block.dir/rule
.PHONY : put-block

# fast build rule for target.
put-block/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/put-block.dir/build.make examples/CMakeFiles/put-block.dir/build
.PHONY : put-block/fast

# Convenience name for target.
examples/CMakeFiles/ring-bcast.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/ring-bcast.dir/rule
.PHONY : examples/CMakeFiles/ring-bcast.dir/rule

# Convenience name for target.
ring-bcast: examples/CMakeFiles/ring-bcast.dir/rule
.PHONY : ring-bcast

# fast build rule for target.
ring-bcast/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/ring-bcast.dir/build.make examples/CMakeFiles/ring-bcast.dir/build
.PHONY : ring-bcast/fast

# Convenience name for target.
examples/CMakeFiles/ring-reduce.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/ring-reduce.dir/rule
.PHONY : examples/CMakeFiles/ring-reduce.dir/rule

# Convenience name for target.
ring-reduce: examples/CMakeFiles/ring-reduce.dir/rule
.PHONY : ring-reduce

# fast build rule for target.
ring-reduce/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/ring-reduce.dir/build.make examples/CMakeFiles/ring-reduce.dir/build
.PHONY : ring-reduce/fast

# Convenience name for target.
examples/CMakeFiles/moe_shuffle.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/moe_shuffle.dir/rule
.PHONY : examples/CMakeFiles/moe_shuffle.dir/rule

# Convenience name for target.
moe_shuffle: examples/CMakeFiles/moe_shuffle.dir/rule
.PHONY : moe_shuffle

# fast build rule for target.
moe_shuffle/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/moe_shuffle.dir/build.make examples/CMakeFiles/moe_shuffle.dir/build
.PHONY : moe_shuffle/fast

# Convenience name for target.
examples/CMakeFiles/user-buffer.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/user-buffer.dir/rule
.PHONY : examples/CMakeFiles/user-buffer.dir/rule

# Convenience name for target.
user-buffer: examples/CMakeFiles/user-buffer.dir/rule
.PHONY : user-buffer

# fast build rule for target.
user-buffer/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/user-buffer.dir/build.make examples/CMakeFiles/user-buffer.dir/build
.PHONY : user-buffer/fast

collective-launch.o: collective-launch.cu.o
.PHONY : collective-launch.o

# target to build an object file
collective-launch.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/collective-launch.dir/build.make examples/CMakeFiles/collective-launch.dir/collective-launch.cu.o
.PHONY : collective-launch.cu.o

collective-launch.i: collective-launch.cu.i
.PHONY : collective-launch.i

# target to preprocess a source file
collective-launch.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/collective-launch.dir/build.make examples/CMakeFiles/collective-launch.dir/collective-launch.cu.i
.PHONY : collective-launch.cu.i

collective-launch.s: collective-launch.cu.s
.PHONY : collective-launch.s

# target to generate assembly for a file
collective-launch.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/collective-launch.dir/build.make examples/CMakeFiles/collective-launch.dir/collective-launch.cu.s
.PHONY : collective-launch.cu.s

dev-guide-ring.o: dev-guide-ring.cu.o
.PHONY : dev-guide-ring.o

# target to build an object file
dev-guide-ring.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/dev-guide-ring.dir/build.make examples/CMakeFiles/dev-guide-ring.dir/dev-guide-ring.cu.o
.PHONY : dev-guide-ring.cu.o

dev-guide-ring.i: dev-guide-ring.cu.i
.PHONY : dev-guide-ring.i

# target to preprocess a source file
dev-guide-ring.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/dev-guide-ring.dir/build.make examples/CMakeFiles/dev-guide-ring.dir/dev-guide-ring.cu.i
.PHONY : dev-guide-ring.cu.i

dev-guide-ring.s: dev-guide-ring.cu.s
.PHONY : dev-guide-ring.s

# target to generate assembly for a file
dev-guide-ring.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/dev-guide-ring.dir/build.make examples/CMakeFiles/dev-guide-ring.dir/dev-guide-ring.cu.s
.PHONY : dev-guide-ring.cu.s

moe_shuffle.o: moe_shuffle.cu.o
.PHONY : moe_shuffle.o

# target to build an object file
moe_shuffle.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/moe_shuffle.dir/build.make examples/CMakeFiles/moe_shuffle.dir/moe_shuffle.cu.o
.PHONY : moe_shuffle.cu.o

moe_shuffle.i: moe_shuffle.cu.i
.PHONY : moe_shuffle.i

# target to preprocess a source file
moe_shuffle.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/moe_shuffle.dir/build.make examples/CMakeFiles/moe_shuffle.dir/moe_shuffle.cu.i
.PHONY : moe_shuffle.cu.i

moe_shuffle.s: moe_shuffle.cu.s
.PHONY : moe_shuffle.s

# target to generate assembly for a file
moe_shuffle.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/moe_shuffle.dir/build.make examples/CMakeFiles/moe_shuffle.dir/moe_shuffle.cu.s
.PHONY : moe_shuffle.cu.s

on-stream.o: on-stream.cu.o
.PHONY : on-stream.o

# target to build an object file
on-stream.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/on-stream.dir/build.make examples/CMakeFiles/on-stream.dir/on-stream.cu.o
.PHONY : on-stream.cu.o

on-stream.i: on-stream.cu.i
.PHONY : on-stream.i

# target to preprocess a source file
on-stream.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/on-stream.dir/build.make examples/CMakeFiles/on-stream.dir/on-stream.cu.i
.PHONY : on-stream.cu.i

on-stream.s: on-stream.cu.s
.PHONY : on-stream.s

# target to generate assembly for a file
on-stream.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/on-stream.dir/build.make examples/CMakeFiles/on-stream.dir/on-stream.cu.s
.PHONY : on-stream.cu.s

put-block.o: put-block.cu.o
.PHONY : put-block.o

# target to build an object file
put-block.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/put-block.dir/build.make examples/CMakeFiles/put-block.dir/put-block.cu.o
.PHONY : put-block.cu.o

put-block.i: put-block.cu.i
.PHONY : put-block.i

# target to preprocess a source file
put-block.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/put-block.dir/build.make examples/CMakeFiles/put-block.dir/put-block.cu.i
.PHONY : put-block.cu.i

put-block.s: put-block.cu.s
.PHONY : put-block.s

# target to generate assembly for a file
put-block.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/put-block.dir/build.make examples/CMakeFiles/put-block.dir/put-block.cu.s
.PHONY : put-block.cu.s

ring-bcast.o: ring-bcast.cu.o
.PHONY : ring-bcast.o

# target to build an object file
ring-bcast.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/ring-bcast.dir/build.make examples/CMakeFiles/ring-bcast.dir/ring-bcast.cu.o
.PHONY : ring-bcast.cu.o

ring-bcast.i: ring-bcast.cu.i
.PHONY : ring-bcast.i

# target to preprocess a source file
ring-bcast.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/ring-bcast.dir/build.make examples/CMakeFiles/ring-bcast.dir/ring-bcast.cu.i
.PHONY : ring-bcast.cu.i

ring-bcast.s: ring-bcast.cu.s
.PHONY : ring-bcast.s

# target to generate assembly for a file
ring-bcast.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/ring-bcast.dir/build.make examples/CMakeFiles/ring-bcast.dir/ring-bcast.cu.s
.PHONY : ring-bcast.cu.s

ring-reduce.o: ring-reduce.cu.o
.PHONY : ring-reduce.o

# target to build an object file
ring-reduce.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/ring-reduce.dir/build.make examples/CMakeFiles/ring-reduce.dir/ring-reduce.cu.o
.PHONY : ring-reduce.cu.o

ring-reduce.i: ring-reduce.cu.i
.PHONY : ring-reduce.i

# target to preprocess a source file
ring-reduce.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/ring-reduce.dir/build.make examples/CMakeFiles/ring-reduce.dir/ring-reduce.cu.i
.PHONY : ring-reduce.cu.i

ring-reduce.s: ring-reduce.cu.s
.PHONY : ring-reduce.s

# target to generate assembly for a file
ring-reduce.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/ring-reduce.dir/build.make examples/CMakeFiles/ring-reduce.dir/ring-reduce.cu.s
.PHONY : ring-reduce.cu.s

thread-group.o: thread-group.cu.o
.PHONY : thread-group.o

# target to build an object file
thread-group.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/thread-group.dir/build.make examples/CMakeFiles/thread-group.dir/thread-group.cu.o
.PHONY : thread-group.cu.o

thread-group.i: thread-group.cu.i
.PHONY : thread-group.i

# target to preprocess a source file
thread-group.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/thread-group.dir/build.make examples/CMakeFiles/thread-group.dir/thread-group.cu.i
.PHONY : thread-group.cu.i

thread-group.s: thread-group.cu.s
.PHONY : thread-group.s

# target to generate assembly for a file
thread-group.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/thread-group.dir/build.make examples/CMakeFiles/thread-group.dir/thread-group.cu.s
.PHONY : thread-group.cu.s

user-buffer.o: user-buffer.cu.o
.PHONY : user-buffer.o

# target to build an object file
user-buffer.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/user-buffer.dir/build.make examples/CMakeFiles/user-buffer.dir/user-buffer.cu.o
.PHONY : user-buffer.cu.o

user-buffer.i: user-buffer.cu.i
.PHONY : user-buffer.i

# target to preprocess a source file
user-buffer.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/user-buffer.dir/build.make examples/CMakeFiles/user-buffer.dir/user-buffer.cu.i
.PHONY : user-buffer.cu.i

user-buffer.s: user-buffer.cu.s
.PHONY : user-buffer.s

# target to generate assembly for a file
user-buffer.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/user-buffer.dir/build.make examples/CMakeFiles/user-buffer.dir/user-buffer.cu.s
.PHONY : user-buffer.cu.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... package"
	@echo "... package_source"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... collective-launch"
	@echo "... dev-guide-ring"
	@echo "... moe_shuffle"
	@echo "... on-stream"
	@echo "... put-block"
	@echo "... ring-bcast"
	@echo "... ring-reduce"
	@echo "... thread-group"
	@echo "... user-buffer"
	@echo "... collective-launch.o"
	@echo "... collective-launch.i"
	@echo "... collective-launch.s"
	@echo "... dev-guide-ring.o"
	@echo "... dev-guide-ring.i"
	@echo "... dev-guide-ring.s"
	@echo "... moe_shuffle.o"
	@echo "... moe_shuffle.i"
	@echo "... moe_shuffle.s"
	@echo "... on-stream.o"
	@echo "... on-stream.i"
	@echo "... on-stream.s"
	@echo "... put-block.o"
	@echo "... put-block.i"
	@echo "... put-block.s"
	@echo "... ring-bcast.o"
	@echo "... ring-bcast.i"
	@echo "... ring-bcast.s"
	@echo "... ring-reduce.o"
	@echo "... ring-reduce.i"
	@echo "... ring-reduce.s"
	@echo "... thread-group.o"
	@echo "... thread-group.i"
	@echo "... thread-group.s"
	@echo "... user-buffer.o"
	@echo "... user-buffer.i"
	@echo "... user-buffer.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

