# Install script for directory: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/global/homes/j/jackyan/opt/nvshmem-3.3.9")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Release")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Install shared libraries without execute permission?
if(NOT DEFINED CMAKE_INSTALL_SO_NO_EXE)
  set(CMAKE_INSTALL_SO_NO_EXE "0")
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

# Set default install directory permissions.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "/usr/bin/objdump")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/collective-launch")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/collective-launch")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/collective-launch" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/collective-launch")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/collective-launch")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/examples" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/collective-launch")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/collective-launch" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/collective-launch")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/collective-launch")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/on-stream")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/on-stream")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/on-stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/on-stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/on-stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/examples" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/on-stream")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/on-stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/on-stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/on-stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/thread-group")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/thread-group")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/thread-group" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/thread-group")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/thread-group")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/examples" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/thread-group")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/thread-group" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/thread-group")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/thread-group")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/put-block")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/put-block")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/put-block" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/put-block")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/put-block")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/examples" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/put-block")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/put-block" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/put-block")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/put-block")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/dev-guide-ring")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/dev-guide-ring")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/dev-guide-ring" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/dev-guide-ring")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/dev-guide-ring")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/examples" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/dev-guide-ring")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/dev-guide-ring" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/dev-guide-ring")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/dev-guide-ring")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/ring-bcast")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/ring-bcast")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/ring-bcast" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/ring-bcast")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/ring-bcast")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/examples" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/ring-bcast")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/ring-bcast" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/ring-bcast")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/ring-bcast")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/ring-reduce")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/ring-reduce")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/ring-reduce" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/ring-reduce")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/ring-reduce")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/examples" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/ring-reduce")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/ring-reduce" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/ring-reduce")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/ring-reduce")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/moe_shuffle")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/moe_shuffle")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/moe_shuffle" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/moe_shuffle")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/moe_shuffle")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/examples" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/moe_shuffle")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/moe_shuffle" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/moe_shuffle")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/moe_shuffle")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/user-buffer")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/user-buffer")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/user-buffer" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/user-buffer")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/examples/examples_install/user-buffer")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/examples" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/examples/user-buffer")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/user-buffer" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/user-buffer")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/examples/user-buffer")
    endif()
  endif()
endif()

