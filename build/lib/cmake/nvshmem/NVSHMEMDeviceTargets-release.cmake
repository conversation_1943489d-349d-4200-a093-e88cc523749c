#----------------------------------------------------------------
# Generated CMake target import file for configuration "Release".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "nvshmem::nvshmem_device" for configuration "Release"
set_property(TARGET nvshmem::nvshmem_device APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(nvshmem::nvshmem_device PROPERTIES
  IMPORTED_LINK_INTERFACE_LANGUAGES_RELEASE "CUDA;CXX"
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libnvshmem_device.a"
  )

list(APPEND _IMPORT_CHECK_TARGETS nvshmem::nvshmem_device )
list(APPEND _IMPORT_CHECK_FILES_FOR_nvshmem::nvshmem_device "${_IMPORT_PREFIX}/lib/libnvshmem_device.a" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
