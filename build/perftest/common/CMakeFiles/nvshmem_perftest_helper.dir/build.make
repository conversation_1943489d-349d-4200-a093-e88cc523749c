# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build

# Include any dependencies generated for this target.
include perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/compiler_depend.make

# Include the progress variables for this target.
include perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/progress.make

# Include the compile flags for this target's objects.
include perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/flags.make

perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/utils.cu.o: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/flags.make
perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/utils.cu.o: ../perftest/common/utils.cu
perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/utils.cu.o: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CUDA object perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/utils.cu.o"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/common && /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/nvcc -forward-unknown-to-host-compiler $(CUDA_DEFINES) $(CUDA_INCLUDES) $(CUDA_FLAGS) -MD -MT perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/utils.cu.o -MF CMakeFiles/nvshmem_perftest_helper.dir/utils.cu.o.d -x cu -dc /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/common/utils.cu -o CMakeFiles/nvshmem_perftest_helper.dir/utils.cu.o

perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/utils.cu.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CUDA source to CMakeFiles/nvshmem_perftest_helper.dir/utils.cu.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_PREPROCESSED_SOURCE

perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/utils.cu.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CUDA source to assembly CMakeFiles/nvshmem_perftest_helper.dir/utils.cu.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_ASSEMBLY_SOURCE

# Object files for target nvshmem_perftest_helper
nvshmem_perftest_helper_OBJECTS = \
"CMakeFiles/nvshmem_perftest_helper.dir/utils.cu.o"

# External object files for target nvshmem_perftest_helper
nvshmem_perftest_helper_EXTERNAL_OBJECTS =

perftest/common/libnvshmem_perftest_helper.a: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/utils.cu.o
perftest/common/libnvshmem_perftest_helper.a: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/build.make
perftest/common/libnvshmem_perftest_helper.a: perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CUDA static library libnvshmem_perftest_helper.a"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/common && $(CMAKE_COMMAND) -P CMakeFiles/nvshmem_perftest_helper.dir/cmake_clean_target.cmake
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/common && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/nvshmem_perftest_helper.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/build: perftest/common/libnvshmem_perftest_helper.a
.PHONY : perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/build

perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/clean:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/common && $(CMAKE_COMMAND) -P CMakeFiles/nvshmem_perftest_helper.dir/cmake_clean.cmake
.PHONY : perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/clean

perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/depend:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9 /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/common /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/common /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : perftest/common/CMakeFiles/nvshmem_perftest_helper.dir/depend

