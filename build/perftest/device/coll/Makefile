# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && /usr/bin/cpack --config ./CPackSourceConfig.cmake /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source
.PHONY : package_source/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && /usr/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package
.PHONY : package/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# The main all target
all: cmake_check_build_system
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll//CMakeFiles/progress.marks
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/rule
.PHONY : perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/rule

# Convenience name for target.
perf_device_alltoall_latency: perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/rule
.PHONY : perf_device_alltoall_latency

# fast build rule for target.
perf_device_alltoall_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/build
.PHONY : perf_device_alltoall_latency/fast

# Convenience name for target.
perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/rule
.PHONY : perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/rule

# Convenience name for target.
perf_device_fcollect_latency: perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/rule
.PHONY : perf_device_fcollect_latency

# fast build rule for target.
perf_device_fcollect_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/build
.PHONY : perf_device_fcollect_latency/fast

# Convenience name for target.
perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/rule
.PHONY : perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/rule

# Convenience name for target.
perf_device_bcast_latency: perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/rule
.PHONY : perf_device_bcast_latency

# fast build rule for target.
perf_device_bcast_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/build
.PHONY : perf_device_bcast_latency/fast

# Convenience name for target.
perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/rule
.PHONY : perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/rule

# Convenience name for target.
perf_device_reduction_latency: perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/rule
.PHONY : perf_device_reduction_latency

# fast build rule for target.
perf_device_reduction_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/build
.PHONY : perf_device_reduction_latency/fast

# Convenience name for target.
perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/rule
.PHONY : perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/rule

# Convenience name for target.
perf_device_reducescatter_latency: perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/rule
.PHONY : perf_device_reducescatter_latency

# fast build rule for target.
perf_device_reducescatter_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/build
.PHONY : perf_device_reducescatter_latency/fast

# Convenience name for target.
perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/rule
.PHONY : perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/rule

# Convenience name for target.
perf_device_barrier_latency: perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/rule
.PHONY : perf_device_barrier_latency

# fast build rule for target.
perf_device_barrier_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/build
.PHONY : perf_device_barrier_latency/fast

# Convenience name for target.
perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/rule
.PHONY : perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/rule

# Convenience name for target.
perf_device_sync_latency: perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/rule
.PHONY : perf_device_sync_latency

# fast build rule for target.
perf_device_sync_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/build
.PHONY : perf_device_sync_latency/fast

alltoall_latency.o: alltoall_latency.cu.o
.PHONY : alltoall_latency.o

# target to build an object file
alltoall_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/alltoall_latency.cu.o
.PHONY : alltoall_latency.cu.o

alltoall_latency.i: alltoall_latency.cu.i
.PHONY : alltoall_latency.i

# target to preprocess a source file
alltoall_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/alltoall_latency.cu.i
.PHONY : alltoall_latency.cu.i

alltoall_latency.s: alltoall_latency.cu.s
.PHONY : alltoall_latency.s

# target to generate assembly for a file
alltoall_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_alltoall_latency.dir/alltoall_latency.cu.s
.PHONY : alltoall_latency.cu.s

barrier_latency.o: barrier_latency.cu.o
.PHONY : barrier_latency.o

# target to build an object file
barrier_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/barrier_latency.cu.o
.PHONY : barrier_latency.cu.o

barrier_latency.i: barrier_latency.cu.i
.PHONY : barrier_latency.i

# target to preprocess a source file
barrier_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/barrier_latency.cu.i
.PHONY : barrier_latency.cu.i

barrier_latency.s: barrier_latency.cu.s
.PHONY : barrier_latency.s

# target to generate assembly for a file
barrier_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_barrier_latency.dir/barrier_latency.cu.s
.PHONY : barrier_latency.cu.s

bcast_latency.o: bcast_latency.cu.o
.PHONY : bcast_latency.o

# target to build an object file
bcast_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/bcast_latency.cu.o
.PHONY : bcast_latency.cu.o

bcast_latency.i: bcast_latency.cu.i
.PHONY : bcast_latency.i

# target to preprocess a source file
bcast_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/bcast_latency.cu.i
.PHONY : bcast_latency.cu.i

bcast_latency.s: bcast_latency.cu.s
.PHONY : bcast_latency.s

# target to generate assembly for a file
bcast_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_bcast_latency.dir/bcast_latency.cu.s
.PHONY : bcast_latency.cu.s

fcollect_latency.o: fcollect_latency.cu.o
.PHONY : fcollect_latency.o

# target to build an object file
fcollect_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/fcollect_latency.cu.o
.PHONY : fcollect_latency.cu.o

fcollect_latency.i: fcollect_latency.cu.i
.PHONY : fcollect_latency.i

# target to preprocess a source file
fcollect_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/fcollect_latency.cu.i
.PHONY : fcollect_latency.cu.i

fcollect_latency.s: fcollect_latency.cu.s
.PHONY : fcollect_latency.s

# target to generate assembly for a file
fcollect_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_fcollect_latency.dir/fcollect_latency.cu.s
.PHONY : fcollect_latency.cu.s

reducescatter_latency.o: reducescatter_latency.cu.o
.PHONY : reducescatter_latency.o

# target to build an object file
reducescatter_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/reducescatter_latency.cu.o
.PHONY : reducescatter_latency.cu.o

reducescatter_latency.i: reducescatter_latency.cu.i
.PHONY : reducescatter_latency.i

# target to preprocess a source file
reducescatter_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/reducescatter_latency.cu.i
.PHONY : reducescatter_latency.cu.i

reducescatter_latency.s: reducescatter_latency.cu.s
.PHONY : reducescatter_latency.s

# target to generate assembly for a file
reducescatter_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_reducescatter_latency.dir/reducescatter_latency.cu.s
.PHONY : reducescatter_latency.cu.s

reduction_latency.o: reduction_latency.cu.o
.PHONY : reduction_latency.o

# target to build an object file
reduction_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/reduction_latency.cu.o
.PHONY : reduction_latency.cu.o

reduction_latency.i: reduction_latency.cu.i
.PHONY : reduction_latency.i

# target to preprocess a source file
reduction_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/reduction_latency.cu.i
.PHONY : reduction_latency.cu.i

reduction_latency.s: reduction_latency.cu.s
.PHONY : reduction_latency.s

# target to generate assembly for a file
reduction_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_reduction_latency.dir/reduction_latency.cu.s
.PHONY : reduction_latency.cu.s

sync_latency.o: sync_latency.cu.o
.PHONY : sync_latency.o

# target to build an object file
sync_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/sync_latency.cu.o
.PHONY : sync_latency.cu.o

sync_latency.i: sync_latency.cu.i
.PHONY : sync_latency.i

# target to preprocess a source file
sync_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/sync_latency.cu.i
.PHONY : sync_latency.cu.i

sync_latency.s: sync_latency.cu.s
.PHONY : sync_latency.s

# target to generate assembly for a file
sync_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/build.make perftest/device/coll/CMakeFiles/perf_device_sync_latency.dir/sync_latency.cu.s
.PHONY : sync_latency.cu.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... package"
	@echo "... package_source"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... perf_device_alltoall_latency"
	@echo "... perf_device_barrier_latency"
	@echo "... perf_device_bcast_latency"
	@echo "... perf_device_fcollect_latency"
	@echo "... perf_device_reducescatter_latency"
	@echo "... perf_device_reduction_latency"
	@echo "... perf_device_sync_latency"
	@echo "... alltoall_latency.o"
	@echo "... alltoall_latency.i"
	@echo "... alltoall_latency.s"
	@echo "... barrier_latency.o"
	@echo "... barrier_latency.i"
	@echo "... barrier_latency.s"
	@echo "... bcast_latency.o"
	@echo "... bcast_latency.i"
	@echo "... bcast_latency.s"
	@echo "... fcollect_latency.o"
	@echo "... fcollect_latency.i"
	@echo "... fcollect_latency.s"
	@echo "... reducescatter_latency.o"
	@echo "... reducescatter_latency.i"
	@echo "... reducescatter_latency.s"
	@echo "... reduction_latency.o"
	@echo "... reduction_latency.i"
	@echo "... reduction_latency.s"
	@echo "... sync_latency.o"
	@echo "... sync_latency.i"
	@echo "... sync_latency.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

