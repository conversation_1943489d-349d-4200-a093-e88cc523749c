# Install script for directory: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/device/coll

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/global/homes/j/jackyan/opt/nvshmem-3.3.9")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Release")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Install shared libraries without execute permission?
if(NOT DEFINED CMAKE_INSTALL_SO_NO_EXE)
  set(CMAKE_INSTALL_SO_NO_EXE "0")
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

# Set default install directory permissions.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "/usr/bin/objdump")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/alltoall_latency")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll/alltoall_latency")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/alltoall_latency" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/alltoall_latency")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/alltoall_latency")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll/alltoall_latency")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/alltoall_latency" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/alltoall_latency")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/alltoall_latency")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/barrier_latency")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll/barrier_latency")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/barrier_latency" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/barrier_latency")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/barrier_latency")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll/barrier_latency")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/barrier_latency" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/barrier_latency")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/barrier_latency")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/bcast_latency")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll/bcast_latency")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/bcast_latency" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/bcast_latency")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/bcast_latency")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll/bcast_latency")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/bcast_latency" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/bcast_latency")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/bcast_latency")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/fcollect_latency")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll/fcollect_latency")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/fcollect_latency" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/fcollect_latency")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/fcollect_latency")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll/fcollect_latency")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/fcollect_latency" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/fcollect_latency")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/fcollect_latency")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/reducescatter_latency")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll/reducescatter_latency")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/reducescatter_latency" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/reducescatter_latency")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/reducescatter_latency")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll/reducescatter_latency")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/reducescatter_latency" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/reducescatter_latency")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/reducescatter_latency")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/reduction_latency")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll/reduction_latency")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/reduction_latency" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/reduction_latency")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/reduction_latency")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll/reduction_latency")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/reduction_latency" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/reduction_latency")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/reduction_latency")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/sync_latency")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll/sync_latency")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/sync_latency" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/sync_latency")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/device/coll/sync_latency")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/coll/sync_latency")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/sync_latency" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/sync_latency")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/device/coll/sync_latency")
    endif()
  endif()
endif()

