/opt/cray/pe/gcc-native/13/bin/g++ -Wl,--enable-new-dtags CMakeFiles/perf_device_shmem_get_latency.dir/shmem_get_latency.cu.o CMakeFiles/perf_device_shmem_get_latency.dir/cmake_device_link.o -o shmem_get_latency  -Wl,-rpath,"\$ORIGIN/../../../../lib" ../../common/libnvshmem_perftest_helper.a /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/lib64/libcudart.so /usr/lib64/libcuda.so ../../../src/lib/libnvshmem_host.so.3.3.9 ../../../src/lib/libnvshmem_device.a -lcudadevrt -lcudart_static -lrt -lpthread -ldl  -L"/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs" -L"/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib"
