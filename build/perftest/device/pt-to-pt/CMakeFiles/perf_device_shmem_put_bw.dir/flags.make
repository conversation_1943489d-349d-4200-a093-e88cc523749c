# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# compile CU<PERSON> with /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/nvcc
CUDA_DEFINES = -D__STDC_CONSTANT_MACROS -D__STDC_LIMIT_MACROS

CUDA_INCLUDES = -I/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/common -I/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/src/include -isystem=/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/include

CUDA_FLAGS = -O3 -DNDEBUG --generate-code=arch=compute_80,code=[compute_80,sm_80] -Xcompiler=-fPIE -t4 -std=c++17

