# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build

# Include any dependencies generated for this target.
include perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/compiler_depend.make

# Include the progress variables for this target.
include perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/progress.make

# Include the compile flags for this target's objects.
include perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/flags.make

perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.o: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/flags.make
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.o: ../perftest/device/pt-to-pt/shmem_put_signal_ping_pong_latency.cu
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.o: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CUDA object perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.o"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/pt-to-pt && /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/nvcc -forward-unknown-to-host-compiler $(CUDA_DEFINES) $(CUDA_INCLUDES) $(CUDA_FLAGS) -MD -MT perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.o -MF CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.o.d -x cu -dc /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/device/pt-to-pt/shmem_put_signal_ping_pong_latency.cu -o CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.o

perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CUDA source to CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_PREPROCESSED_SOURCE

perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CUDA source to assembly CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_ASSEMBLY_SOURCE

# Object files for target perf_device_shmem_put_signal_ping_pong_latency
perf_device_shmem_put_signal_ping_pong_latency_OBJECTS = \
"CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.o"

# External object files for target perf_device_shmem_put_signal_ping_pong_latency
perf_device_shmem_put_signal_ping_pong_latency_EXTERNAL_OBJECTS =

perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/cmake_device_link.o: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.o
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/cmake_device_link.o: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build.make
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/cmake_device_link.o: perftest/common/libnvshmem_perftest_helper.a
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/cmake_device_link.o: /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/lib64/libcudart.so
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/cmake_device_link.o: /usr/lib64/libcuda.so
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/cmake_device_link.o: src/lib/libnvshmem_host.so.3.3.9
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/cmake_device_link.o: src/lib/libnvshmem_device.a
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/cmake_device_link.o: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/dlink.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CUDA device code CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/cmake_device_link.o"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/pt-to-pt && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/dlink.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/cmake_device_link.o
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build

# Object files for target perf_device_shmem_put_signal_ping_pong_latency
perf_device_shmem_put_signal_ping_pong_latency_OBJECTS = \
"CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.o"

# External object files for target perf_device_shmem_put_signal_ping_pong_latency
perf_device_shmem_put_signal_ping_pong_latency_EXTERNAL_OBJECTS =

perftest/device/pt-to-pt/shmem_put_signal_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.o
perftest/device/pt-to-pt/shmem_put_signal_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build.make
perftest/device/pt-to-pt/shmem_put_signal_ping_pong_latency: perftest/common/libnvshmem_perftest_helper.a
perftest/device/pt-to-pt/shmem_put_signal_ping_pong_latency: /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/lib64/libcudart.so
perftest/device/pt-to-pt/shmem_put_signal_ping_pong_latency: /usr/lib64/libcuda.so
perftest/device/pt-to-pt/shmem_put_signal_ping_pong_latency: src/lib/libnvshmem_host.so.3.3.9
perftest/device/pt-to-pt/shmem_put_signal_ping_pong_latency: src/lib/libnvshmem_device.a
perftest/device/pt-to-pt/shmem_put_signal_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/cmake_device_link.o
perftest/device/pt-to-pt/shmem_put_signal_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CUDA executable shmem_put_signal_ping_pong_latency"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/pt-to-pt && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build: perftest/device/pt-to-pt/shmem_put_signal_ping_pong_latency
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build

perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/clean:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/pt-to-pt && $(CMAKE_COMMAND) -P CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/cmake_clean.cmake
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/clean

perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/depend:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9 /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/device/pt-to-pt /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/pt-to-pt /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/depend

