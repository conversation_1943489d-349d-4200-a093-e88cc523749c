# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && /usr/bin/cpack --config ./CPackSourceConfig.cmake /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source
.PHONY : package_source/fast

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && /usr/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package
.PHONY : package/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# The main all target
all: cmake_check_build_system
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/device/pt-to-pt//CMakeFiles/progress.marks
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/all
	$(CMAKE_COMMAND) -E cmake_progress_start /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/rule

# Convenience name for target.
perf_device_shmem_st_bw: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/rule
.PHONY : perf_device_shmem_st_bw

# fast build rule for target.
perf_device_shmem_st_bw/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/build
.PHONY : perf_device_shmem_st_bw/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/rule

# Convenience name for target.
perf_device_shmem_put_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/rule
.PHONY : perf_device_shmem_put_ping_pong_latency

# fast build rule for target.
perf_device_shmem_put_ping_pong_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/build
.PHONY : perf_device_shmem_put_ping_pong_latency/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/rule

# Convenience name for target.
perf_device_shmem_atomic_bw: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/rule
.PHONY : perf_device_shmem_atomic_bw

# fast build rule for target.
perf_device_shmem_atomic_bw/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/build
.PHONY : perf_device_shmem_atomic_bw/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/rule

# Convenience name for target.
perf_device_shmem_p_bw: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/rule
.PHONY : perf_device_shmem_p_bw

# fast build rule for target.
perf_device_shmem_p_bw/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/build
.PHONY : perf_device_shmem_p_bw/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/rule

# Convenience name for target.
perf_device_shmem_put_signal_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/rule
.PHONY : perf_device_shmem_put_signal_ping_pong_latency

# fast build rule for target.
perf_device_shmem_put_signal_ping_pong_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build
.PHONY : perf_device_shmem_put_signal_ping_pong_latency/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/rule

# Convenience name for target.
perf_device_shmem_put_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/rule
.PHONY : perf_device_shmem_put_latency

# fast build rule for target.
perf_device_shmem_put_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/build
.PHONY : perf_device_shmem_put_latency/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/rule

# Convenience name for target.
perf_device_shmem_atomic_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/rule
.PHONY : perf_device_shmem_atomic_latency

# fast build rule for target.
perf_device_shmem_atomic_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/build
.PHONY : perf_device_shmem_atomic_latency/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/rule

# Convenience name for target.
perf_device_shmem_atomic_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/rule
.PHONY : perf_device_shmem_atomic_ping_pong_latency

# fast build rule for target.
perf_device_shmem_atomic_ping_pong_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/build
.PHONY : perf_device_shmem_atomic_ping_pong_latency/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/rule

# Convenience name for target.
perf_device_shmem_g_bw: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/rule
.PHONY : perf_device_shmem_g_bw

# fast build rule for target.
perf_device_shmem_g_bw/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/build
.PHONY : perf_device_shmem_g_bw/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/rule

# Convenience name for target.
perf_device_shmem_g_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/rule
.PHONY : perf_device_shmem_g_latency

# fast build rule for target.
perf_device_shmem_g_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/build
.PHONY : perf_device_shmem_g_latency/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/rule

# Convenience name for target.
perf_device_shmem_p_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/rule
.PHONY : perf_device_shmem_p_ping_pong_latency

# fast build rule for target.
perf_device_shmem_p_ping_pong_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/build
.PHONY : perf_device_shmem_p_ping_pong_latency/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/rule

# Convenience name for target.
perf_device_shmem_get_bw: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/rule
.PHONY : perf_device_shmem_get_bw

# fast build rule for target.
perf_device_shmem_get_bw/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/build
.PHONY : perf_device_shmem_get_bw/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/rule

# Convenience name for target.
perf_device_shmem_get_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/rule
.PHONY : perf_device_shmem_get_latency

# fast build rule for target.
perf_device_shmem_get_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/build
.PHONY : perf_device_shmem_get_latency/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/rule

# Convenience name for target.
perf_device_shmem_signal_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/rule
.PHONY : perf_device_shmem_signal_ping_pong_latency

# fast build rule for target.
perf_device_shmem_signal_ping_pong_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/build
.PHONY : perf_device_shmem_signal_ping_pong_latency/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/rule

# Convenience name for target.
perf_device_shmem_p_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/rule
.PHONY : perf_device_shmem_p_latency

# fast build rule for target.
perf_device_shmem_p_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/build
.PHONY : perf_device_shmem_p_latency/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/rule

# Convenience name for target.
perf_device_shmem_put_atomic_ping_pong_latency: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/rule
.PHONY : perf_device_shmem_put_atomic_ping_pong_latency

# fast build rule for target.
perf_device_shmem_put_atomic_ping_pong_latency/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/build
.PHONY : perf_device_shmem_put_atomic_ping_pong_latency/fast

# Convenience name for target.
perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/rule:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/rule
.PHONY : perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/rule

# Convenience name for target.
perf_device_shmem_put_bw: perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/rule
.PHONY : perf_device_shmem_put_bw

# fast build rule for target.
perf_device_shmem_put_bw/fast:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/build
.PHONY : perf_device_shmem_put_bw/fast

shmem_atomic_bw.o: shmem_atomic_bw.cu.o
.PHONY : shmem_atomic_bw.o

# target to build an object file
shmem_atomic_bw.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/shmem_atomic_bw.cu.o
.PHONY : shmem_atomic_bw.cu.o

shmem_atomic_bw.i: shmem_atomic_bw.cu.i
.PHONY : shmem_atomic_bw.i

# target to preprocess a source file
shmem_atomic_bw.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/shmem_atomic_bw.cu.i
.PHONY : shmem_atomic_bw.cu.i

shmem_atomic_bw.s: shmem_atomic_bw.cu.s
.PHONY : shmem_atomic_bw.s

# target to generate assembly for a file
shmem_atomic_bw.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_bw.dir/shmem_atomic_bw.cu.s
.PHONY : shmem_atomic_bw.cu.s

shmem_atomic_latency.o: shmem_atomic_latency.cu.o
.PHONY : shmem_atomic_latency.o

# target to build an object file
shmem_atomic_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/shmem_atomic_latency.cu.o
.PHONY : shmem_atomic_latency.cu.o

shmem_atomic_latency.i: shmem_atomic_latency.cu.i
.PHONY : shmem_atomic_latency.i

# target to preprocess a source file
shmem_atomic_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/shmem_atomic_latency.cu.i
.PHONY : shmem_atomic_latency.cu.i

shmem_atomic_latency.s: shmem_atomic_latency.cu.s
.PHONY : shmem_atomic_latency.s

# target to generate assembly for a file
shmem_atomic_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_latency.dir/shmem_atomic_latency.cu.s
.PHONY : shmem_atomic_latency.cu.s

shmem_atomic_ping_pong_latency.o: shmem_atomic_ping_pong_latency.cu.o
.PHONY : shmem_atomic_ping_pong_latency.o

# target to build an object file
shmem_atomic_ping_pong_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/shmem_atomic_ping_pong_latency.cu.o
.PHONY : shmem_atomic_ping_pong_latency.cu.o

shmem_atomic_ping_pong_latency.i: shmem_atomic_ping_pong_latency.cu.i
.PHONY : shmem_atomic_ping_pong_latency.i

# target to preprocess a source file
shmem_atomic_ping_pong_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/shmem_atomic_ping_pong_latency.cu.i
.PHONY : shmem_atomic_ping_pong_latency.cu.i

shmem_atomic_ping_pong_latency.s: shmem_atomic_ping_pong_latency.cu.s
.PHONY : shmem_atomic_ping_pong_latency.s

# target to generate assembly for a file
shmem_atomic_ping_pong_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_atomic_ping_pong_latency.dir/shmem_atomic_ping_pong_latency.cu.s
.PHONY : shmem_atomic_ping_pong_latency.cu.s

shmem_g_bw.o: shmem_g_bw.cu.o
.PHONY : shmem_g_bw.o

# target to build an object file
shmem_g_bw.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/shmem_g_bw.cu.o
.PHONY : shmem_g_bw.cu.o

shmem_g_bw.i: shmem_g_bw.cu.i
.PHONY : shmem_g_bw.i

# target to preprocess a source file
shmem_g_bw.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/shmem_g_bw.cu.i
.PHONY : shmem_g_bw.cu.i

shmem_g_bw.s: shmem_g_bw.cu.s
.PHONY : shmem_g_bw.s

# target to generate assembly for a file
shmem_g_bw.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_bw.dir/shmem_g_bw.cu.s
.PHONY : shmem_g_bw.cu.s

shmem_g_latency.o: shmem_g_latency.cu.o
.PHONY : shmem_g_latency.o

# target to build an object file
shmem_g_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/shmem_g_latency.cu.o
.PHONY : shmem_g_latency.cu.o

shmem_g_latency.i: shmem_g_latency.cu.i
.PHONY : shmem_g_latency.i

# target to preprocess a source file
shmem_g_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/shmem_g_latency.cu.i
.PHONY : shmem_g_latency.cu.i

shmem_g_latency.s: shmem_g_latency.cu.s
.PHONY : shmem_g_latency.s

# target to generate assembly for a file
shmem_g_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_g_latency.dir/shmem_g_latency.cu.s
.PHONY : shmem_g_latency.cu.s

shmem_get_bw.o: shmem_get_bw.cu.o
.PHONY : shmem_get_bw.o

# target to build an object file
shmem_get_bw.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/shmem_get_bw.cu.o
.PHONY : shmem_get_bw.cu.o

shmem_get_bw.i: shmem_get_bw.cu.i
.PHONY : shmem_get_bw.i

# target to preprocess a source file
shmem_get_bw.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/shmem_get_bw.cu.i
.PHONY : shmem_get_bw.cu.i

shmem_get_bw.s: shmem_get_bw.cu.s
.PHONY : shmem_get_bw.s

# target to generate assembly for a file
shmem_get_bw.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_bw.dir/shmem_get_bw.cu.s
.PHONY : shmem_get_bw.cu.s

shmem_get_latency.o: shmem_get_latency.cu.o
.PHONY : shmem_get_latency.o

# target to build an object file
shmem_get_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/shmem_get_latency.cu.o
.PHONY : shmem_get_latency.cu.o

shmem_get_latency.i: shmem_get_latency.cu.i
.PHONY : shmem_get_latency.i

# target to preprocess a source file
shmem_get_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/shmem_get_latency.cu.i
.PHONY : shmem_get_latency.cu.i

shmem_get_latency.s: shmem_get_latency.cu.s
.PHONY : shmem_get_latency.s

# target to generate assembly for a file
shmem_get_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_get_latency.dir/shmem_get_latency.cu.s
.PHONY : shmem_get_latency.cu.s

shmem_p_bw.o: shmem_p_bw.cu.o
.PHONY : shmem_p_bw.o

# target to build an object file
shmem_p_bw.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/shmem_p_bw.cu.o
.PHONY : shmem_p_bw.cu.o

shmem_p_bw.i: shmem_p_bw.cu.i
.PHONY : shmem_p_bw.i

# target to preprocess a source file
shmem_p_bw.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/shmem_p_bw.cu.i
.PHONY : shmem_p_bw.cu.i

shmem_p_bw.s: shmem_p_bw.cu.s
.PHONY : shmem_p_bw.s

# target to generate assembly for a file
shmem_p_bw.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_bw.dir/shmem_p_bw.cu.s
.PHONY : shmem_p_bw.cu.s

shmem_p_latency.o: shmem_p_latency.cu.o
.PHONY : shmem_p_latency.o

# target to build an object file
shmem_p_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/shmem_p_latency.cu.o
.PHONY : shmem_p_latency.cu.o

shmem_p_latency.i: shmem_p_latency.cu.i
.PHONY : shmem_p_latency.i

# target to preprocess a source file
shmem_p_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/shmem_p_latency.cu.i
.PHONY : shmem_p_latency.cu.i

shmem_p_latency.s: shmem_p_latency.cu.s
.PHONY : shmem_p_latency.s

# target to generate assembly for a file
shmem_p_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_latency.dir/shmem_p_latency.cu.s
.PHONY : shmem_p_latency.cu.s

shmem_p_ping_pong_latency.o: shmem_p_ping_pong_latency.cu.o
.PHONY : shmem_p_ping_pong_latency.o

# target to build an object file
shmem_p_ping_pong_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/shmem_p_ping_pong_latency.cu.o
.PHONY : shmem_p_ping_pong_latency.cu.o

shmem_p_ping_pong_latency.i: shmem_p_ping_pong_latency.cu.i
.PHONY : shmem_p_ping_pong_latency.i

# target to preprocess a source file
shmem_p_ping_pong_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/shmem_p_ping_pong_latency.cu.i
.PHONY : shmem_p_ping_pong_latency.cu.i

shmem_p_ping_pong_latency.s: shmem_p_ping_pong_latency.cu.s
.PHONY : shmem_p_ping_pong_latency.s

# target to generate assembly for a file
shmem_p_ping_pong_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_p_ping_pong_latency.dir/shmem_p_ping_pong_latency.cu.s
.PHONY : shmem_p_ping_pong_latency.cu.s

shmem_put_atomic_ping_pong_latency.o: shmem_put_atomic_ping_pong_latency.cu.o
.PHONY : shmem_put_atomic_ping_pong_latency.o

# target to build an object file
shmem_put_atomic_ping_pong_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/shmem_put_atomic_ping_pong_latency.cu.o
.PHONY : shmem_put_atomic_ping_pong_latency.cu.o

shmem_put_atomic_ping_pong_latency.i: shmem_put_atomic_ping_pong_latency.cu.i
.PHONY : shmem_put_atomic_ping_pong_latency.i

# target to preprocess a source file
shmem_put_atomic_ping_pong_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/shmem_put_atomic_ping_pong_latency.cu.i
.PHONY : shmem_put_atomic_ping_pong_latency.cu.i

shmem_put_atomic_ping_pong_latency.s: shmem_put_atomic_ping_pong_latency.cu.s
.PHONY : shmem_put_atomic_ping_pong_latency.s

# target to generate assembly for a file
shmem_put_atomic_ping_pong_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_atomic_ping_pong_latency.dir/shmem_put_atomic_ping_pong_latency.cu.s
.PHONY : shmem_put_atomic_ping_pong_latency.cu.s

shmem_put_bw.o: shmem_put_bw.cu.o
.PHONY : shmem_put_bw.o

# target to build an object file
shmem_put_bw.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/shmem_put_bw.cu.o
.PHONY : shmem_put_bw.cu.o

shmem_put_bw.i: shmem_put_bw.cu.i
.PHONY : shmem_put_bw.i

# target to preprocess a source file
shmem_put_bw.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/shmem_put_bw.cu.i
.PHONY : shmem_put_bw.cu.i

shmem_put_bw.s: shmem_put_bw.cu.s
.PHONY : shmem_put_bw.s

# target to generate assembly for a file
shmem_put_bw.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_bw.dir/shmem_put_bw.cu.s
.PHONY : shmem_put_bw.cu.s

shmem_put_latency.o: shmem_put_latency.cu.o
.PHONY : shmem_put_latency.o

# target to build an object file
shmem_put_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/shmem_put_latency.cu.o
.PHONY : shmem_put_latency.cu.o

shmem_put_latency.i: shmem_put_latency.cu.i
.PHONY : shmem_put_latency.i

# target to preprocess a source file
shmem_put_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/shmem_put_latency.cu.i
.PHONY : shmem_put_latency.cu.i

shmem_put_latency.s: shmem_put_latency.cu.s
.PHONY : shmem_put_latency.s

# target to generate assembly for a file
shmem_put_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_latency.dir/shmem_put_latency.cu.s
.PHONY : shmem_put_latency.cu.s

shmem_put_ping_pong_latency.o: shmem_put_ping_pong_latency.cu.o
.PHONY : shmem_put_ping_pong_latency.o

# target to build an object file
shmem_put_ping_pong_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/shmem_put_ping_pong_latency.cu.o
.PHONY : shmem_put_ping_pong_latency.cu.o

shmem_put_ping_pong_latency.i: shmem_put_ping_pong_latency.cu.i
.PHONY : shmem_put_ping_pong_latency.i

# target to preprocess a source file
shmem_put_ping_pong_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/shmem_put_ping_pong_latency.cu.i
.PHONY : shmem_put_ping_pong_latency.cu.i

shmem_put_ping_pong_latency.s: shmem_put_ping_pong_latency.cu.s
.PHONY : shmem_put_ping_pong_latency.s

# target to generate assembly for a file
shmem_put_ping_pong_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_ping_pong_latency.dir/shmem_put_ping_pong_latency.cu.s
.PHONY : shmem_put_ping_pong_latency.cu.s

shmem_put_signal_ping_pong_latency.o: shmem_put_signal_ping_pong_latency.cu.o
.PHONY : shmem_put_signal_ping_pong_latency.o

# target to build an object file
shmem_put_signal_ping_pong_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.o
.PHONY : shmem_put_signal_ping_pong_latency.cu.o

shmem_put_signal_ping_pong_latency.i: shmem_put_signal_ping_pong_latency.cu.i
.PHONY : shmem_put_signal_ping_pong_latency.i

# target to preprocess a source file
shmem_put_signal_ping_pong_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.i
.PHONY : shmem_put_signal_ping_pong_latency.cu.i

shmem_put_signal_ping_pong_latency.s: shmem_put_signal_ping_pong_latency.cu.s
.PHONY : shmem_put_signal_ping_pong_latency.s

# target to generate assembly for a file
shmem_put_signal_ping_pong_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_put_signal_ping_pong_latency.dir/shmem_put_signal_ping_pong_latency.cu.s
.PHONY : shmem_put_signal_ping_pong_latency.cu.s

shmem_signal_ping_pong_latency.o: shmem_signal_ping_pong_latency.cu.o
.PHONY : shmem_signal_ping_pong_latency.o

# target to build an object file
shmem_signal_ping_pong_latency.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/shmem_signal_ping_pong_latency.cu.o
.PHONY : shmem_signal_ping_pong_latency.cu.o

shmem_signal_ping_pong_latency.i: shmem_signal_ping_pong_latency.cu.i
.PHONY : shmem_signal_ping_pong_latency.i

# target to preprocess a source file
shmem_signal_ping_pong_latency.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/shmem_signal_ping_pong_latency.cu.i
.PHONY : shmem_signal_ping_pong_latency.cu.i

shmem_signal_ping_pong_latency.s: shmem_signal_ping_pong_latency.cu.s
.PHONY : shmem_signal_ping_pong_latency.s

# target to generate assembly for a file
shmem_signal_ping_pong_latency.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_signal_ping_pong_latency.dir/shmem_signal_ping_pong_latency.cu.s
.PHONY : shmem_signal_ping_pong_latency.cu.s

shmem_st_bw.o: shmem_st_bw.cu.o
.PHONY : shmem_st_bw.o

# target to build an object file
shmem_st_bw.cu.o:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/shmem_st_bw.cu.o
.PHONY : shmem_st_bw.cu.o

shmem_st_bw.i: shmem_st_bw.cu.i
.PHONY : shmem_st_bw.i

# target to preprocess a source file
shmem_st_bw.cu.i:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/shmem_st_bw.cu.i
.PHONY : shmem_st_bw.cu.i

shmem_st_bw.s: shmem_st_bw.cu.s
.PHONY : shmem_st_bw.s

# target to generate assembly for a file
shmem_st_bw.cu.s:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(MAKE) $(MAKESILENT) -f perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/build.make perftest/device/pt-to-pt/CMakeFiles/perf_device_shmem_st_bw.dir/shmem_st_bw.cu.s
.PHONY : shmem_st_bw.cu.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... package"
	@echo "... package_source"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... perf_device_shmem_atomic_bw"
	@echo "... perf_device_shmem_atomic_latency"
	@echo "... perf_device_shmem_atomic_ping_pong_latency"
	@echo "... perf_device_shmem_g_bw"
	@echo "... perf_device_shmem_g_latency"
	@echo "... perf_device_shmem_get_bw"
	@echo "... perf_device_shmem_get_latency"
	@echo "... perf_device_shmem_p_bw"
	@echo "... perf_device_shmem_p_latency"
	@echo "... perf_device_shmem_p_ping_pong_latency"
	@echo "... perf_device_shmem_put_atomic_ping_pong_latency"
	@echo "... perf_device_shmem_put_bw"
	@echo "... perf_device_shmem_put_latency"
	@echo "... perf_device_shmem_put_ping_pong_latency"
	@echo "... perf_device_shmem_put_signal_ping_pong_latency"
	@echo "... perf_device_shmem_signal_ping_pong_latency"
	@echo "... perf_device_shmem_st_bw"
	@echo "... shmem_atomic_bw.o"
	@echo "... shmem_atomic_bw.i"
	@echo "... shmem_atomic_bw.s"
	@echo "... shmem_atomic_latency.o"
	@echo "... shmem_atomic_latency.i"
	@echo "... shmem_atomic_latency.s"
	@echo "... shmem_atomic_ping_pong_latency.o"
	@echo "... shmem_atomic_ping_pong_latency.i"
	@echo "... shmem_atomic_ping_pong_latency.s"
	@echo "... shmem_g_bw.o"
	@echo "... shmem_g_bw.i"
	@echo "... shmem_g_bw.s"
	@echo "... shmem_g_latency.o"
	@echo "... shmem_g_latency.i"
	@echo "... shmem_g_latency.s"
	@echo "... shmem_get_bw.o"
	@echo "... shmem_get_bw.i"
	@echo "... shmem_get_bw.s"
	@echo "... shmem_get_latency.o"
	@echo "... shmem_get_latency.i"
	@echo "... shmem_get_latency.s"
	@echo "... shmem_p_bw.o"
	@echo "... shmem_p_bw.i"
	@echo "... shmem_p_bw.s"
	@echo "... shmem_p_latency.o"
	@echo "... shmem_p_latency.i"
	@echo "... shmem_p_latency.s"
	@echo "... shmem_p_ping_pong_latency.o"
	@echo "... shmem_p_ping_pong_latency.i"
	@echo "... shmem_p_ping_pong_latency.s"
	@echo "... shmem_put_atomic_ping_pong_latency.o"
	@echo "... shmem_put_atomic_ping_pong_latency.i"
	@echo "... shmem_put_atomic_ping_pong_latency.s"
	@echo "... shmem_put_bw.o"
	@echo "... shmem_put_bw.i"
	@echo "... shmem_put_bw.s"
	@echo "... shmem_put_latency.o"
	@echo "... shmem_put_latency.i"
	@echo "... shmem_put_latency.s"
	@echo "... shmem_put_ping_pong_latency.o"
	@echo "... shmem_put_ping_pong_latency.i"
	@echo "... shmem_put_ping_pong_latency.s"
	@echo "... shmem_put_signal_ping_pong_latency.o"
	@echo "... shmem_put_signal_ping_pong_latency.i"
	@echo "... shmem_put_signal_ping_pong_latency.s"
	@echo "... shmem_signal_ping_pong_latency.o"
	@echo "... shmem_signal_ping_pong_latency.i"
	@echo "... shmem_signal_ping_pong_latency.s"
	@echo "... shmem_st_bw.o"
	@echo "... shmem_st_bw.i"
	@echo "... shmem_st_bw.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

