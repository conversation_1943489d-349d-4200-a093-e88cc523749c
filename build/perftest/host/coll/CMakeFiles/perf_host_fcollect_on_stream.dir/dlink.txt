/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/bin/nvcc -forward-unknown-to-host-compiler -O3 -DNDEBUG --generate-code=arch=compute_80,code=[compute_80,sm_80] -Xcompiler=-fPIC -Wno-deprecated-gpu-targets -shared -dlink CMakeFiles/perf_host_fcollect_on_stream.dir/fcollect_on_stream.cpp.o -o CMakeFiles/perf_host_fcollect_on_stream.dir/cmake_device_link.o   -L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs  -L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib  -L/usr/lib64/gcc/x86_64-suse-linux/13  ../../common/libnvshmem_perftest_helper.a  ../../../src/lib/libnvshmem_device.a -lcudadevrt -lcudart_static -lrt -lpthread -ldl 
