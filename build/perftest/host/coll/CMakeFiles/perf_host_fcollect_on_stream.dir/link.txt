/usr/bin/c++ -O3 -DNDEBUG -Wl,--enable-new-dtags CMakeFiles/perf_host_fcollect_on_stream.dir/fcollect_on_stream.cpp.o CMakeFiles/perf_host_fcollect_on_stream.dir/cmake_device_link.o -o fcollect_on_stream   -L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib/stubs  -L/opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/targets/x86_64-linux/lib  -L/usr/lib64/gcc/x86_64-suse-linux/13  -Wl,-rpath,"\$ORIGIN/../../../../lib" ../../common/libnvshmem_perftest_helper.a /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/lib64/libcudart.so /usr/lib64/libcuda.so ../../../src/lib/libnvshmem_host.so.3.3.9 ../../../src/lib/libnvshmem_device.a -lcudadevrt -lcudart_static -lrt -lpthread -ldl 
