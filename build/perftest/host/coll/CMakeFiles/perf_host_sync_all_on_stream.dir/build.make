# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build

# Include any dependencies generated for this target.
include perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/compiler_depend.make

# Include the progress variables for this target.
include perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/progress.make

# Include the compile flags for this target's objects.
include perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/flags.make

perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.o: perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/flags.make
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.o: ../perftest/host/coll/sync_all_on_stream.cpp
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.o: perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.o"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.o -MF CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.o.d -o CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.o -c /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/host/coll/sync_all_on_stream.cpp

perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.i"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/host/coll/sync_all_on_stream.cpp > CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.i

perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.s"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/host/coll/sync_all_on_stream.cpp -o CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.s

# Object files for target perf_host_sync_all_on_stream
perf_host_sync_all_on_stream_OBJECTS = \
"CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.o"

# External object files for target perf_host_sync_all_on_stream
perf_host_sync_all_on_stream_EXTERNAL_OBJECTS =

perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/cmake_device_link.o: perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.o
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/cmake_device_link.o: perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/build.make
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/cmake_device_link.o: perftest/common/libnvshmem_perftest_helper.a
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/cmake_device_link.o: /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/lib64/libcudart.so
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/cmake_device_link.o: /usr/lib64/libcuda.so
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/cmake_device_link.o: src/lib/libnvshmem_host.so.3.3.9
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/cmake_device_link.o: src/lib/libnvshmem_device.a
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/cmake_device_link.o: perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/dlink.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CUDA device code CMakeFiles/perf_host_sync_all_on_stream.dir/cmake_device_link.o"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/perf_host_sync_all_on_stream.dir/dlink.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/build: perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/cmake_device_link.o
.PHONY : perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/build

# Object files for target perf_host_sync_all_on_stream
perf_host_sync_all_on_stream_OBJECTS = \
"CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.o"

# External object files for target perf_host_sync_all_on_stream
perf_host_sync_all_on_stream_EXTERNAL_OBJECTS =

perftest/host/coll/sync_all_on_stream: perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/sync_all_on_stream.cpp.o
perftest/host/coll/sync_all_on_stream: perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/build.make
perftest/host/coll/sync_all_on_stream: perftest/common/libnvshmem_perftest_helper.a
perftest/host/coll/sync_all_on_stream: /opt/nvidia/hpc_sdk/Linux_x86_64/24.5/cuda/12.4/lib64/libcudart.so
perftest/host/coll/sync_all_on_stream: /usr/lib64/libcuda.so
perftest/host/coll/sync_all_on_stream: src/lib/libnvshmem_host.so.3.3.9
perftest/host/coll/sync_all_on_stream: src/lib/libnvshmem_device.a
perftest/host/coll/sync_all_on_stream: perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/cmake_device_link.o
perftest/host/coll/sync_all_on_stream: perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable sync_all_on_stream"
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/perf_host_sync_all_on_stream.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/build: perftest/host/coll/sync_all_on_stream
.PHONY : perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/build

perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/clean:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll && $(CMAKE_COMMAND) -P CMakeFiles/perf_host_sync_all_on_stream.dir/cmake_clean.cmake
.PHONY : perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/clean

perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/depend:
	cd /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9 /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/host/coll /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : perftest/host/coll/CMakeFiles/perf_host_sync_all_on_stream.dir/depend

