# Install script for directory: /global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/host/coll

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/global/homes/j/jackyan/opt/nvshmem-3.3.9")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Release")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Install shared libraries without execute permission?
if(NOT DEFINED CMAKE_INSTALL_SO_NO_EXE)
  set(CMAKE_INSTALL_SO_NO_EXE "0")
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

# Set default install directory permissions.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "/usr/bin/objdump")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/alltoall_on_stream")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/alltoall_on_stream")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/alltoall_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/alltoall_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/alltoall_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/alltoall_on_stream")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/alltoall_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/alltoall_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/alltoall_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/barrier_all_on_stream")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/barrier_all_on_stream")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/barrier_all_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/barrier_all_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/barrier_all_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/barrier_all_on_stream")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/barrier_all_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/barrier_all_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/barrier_all_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/barrier_on_stream")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/barrier_on_stream")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/barrier_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/barrier_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/barrier_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/barrier_on_stream")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/barrier_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/barrier_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/barrier_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/broadcast_on_stream")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/broadcast_on_stream")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/broadcast_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/broadcast_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/broadcast_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/broadcast_on_stream")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/broadcast_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/broadcast_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/broadcast_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/fcollect_on_stream")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/fcollect_on_stream")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/fcollect_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/fcollect_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/fcollect_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/fcollect_on_stream")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/fcollect_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/fcollect_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/fcollect_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/reduction_on_stream")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/reduction_on_stream")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/reduction_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/reduction_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/reduction_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/reduction_on_stream")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/reduction_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/reduction_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/reduction_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/reducescatter_on_stream")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/reducescatter_on_stream")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/reducescatter_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/reducescatter_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/reducescatter_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/reducescatter_on_stream")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/reducescatter_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/reducescatter_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/reducescatter_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/sync_all_on_stream")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/sync_all_on_stream")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/sync_all_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/sync_all_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/sync_all_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/sync_all_on_stream")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/sync_all_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/sync_all_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/sync_all_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/sync_on_stream")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/sync_on_stream")
  if(EXISTS "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/sync_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/sync_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/perftest/perftest_install/host/coll/sync_on_stream")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll" TYPE EXECUTABLE FILES "/global/homes/j/jackyan/workspace/gpu_comm/nvshmem-3.3.9/build/perftest/host/coll/sync_on_stream")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/sync_on_stream" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/sync_on_stream")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/perftest/host/coll/sync_on_stream")
    endif()
  endif()
endif()

